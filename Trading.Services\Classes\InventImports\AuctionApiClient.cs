﻿using AngleSharp;
using AngleSharp.Dom;
using Microsoft.AspNetCore.Html;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid.Helpers.Mail.Model;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.InventData;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces.InventImports;

namespace Trading.Services.Classes.InventImports;

/// <summary>
/// Client for interacting with the Auction API
/// </summary>
public class AuctionApiClient : IAuctionApiClient
{
  private readonly HttpClient _httpClient;
  private readonly ILogger<AuctionApiClient> _logger;
  private readonly InventDTO _configuration;
  private string _authToken;
  private DateTime _tokenExpiry = DateTime.MinValue;
  private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
  {
    PropertyNameCaseInsensitive = true,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
  };

  public AuctionApiClient(
      HttpClient httpClient,
      ILogger<AuctionApiClient> logger,
      IOptionsSnapshot<InventDTO> configuration)
  {
    _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));

    // Configure base URL from settings
    _httpClient.BaseAddress = new Uri(_configuration.BaseUrl);

    _jsonOptions.Converters.Add(new FlexibleStringConverter());
    _jsonOptions.Converters.Add(new FlexibleDateTimeConverter());
  }

  /// <summary>
  /// Authenticates with the Auction API and gets an access token
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>True if authentication was successful</returns>
  public async Task<bool> AuthenticateAsync(CancellationToken cancellationToken = default)
  {
    try
    {
      // Check if we already have a valid token
      if (!string.IsNullOrEmpty(_authToken) && _tokenExpiry > DateTime.UtcNow)
      {
        return true;
      }

      var username = _configuration.Username;
      var password = _configuration.Password;

      var authRequest = new
      {
        username,
        password
      };

      var content = new StringContent(
          JsonSerializer.Serialize(authRequest),
          Encoding.UTF8,
          "application/json");

      var response = await _httpClient.PostAsync("/api/webservicelogin", content, cancellationToken);

      if (!response.IsSuccessStatusCode)
      {
        _logger.LogError($"Failed to authenticate with the Auction API: {response.StatusCode}");
        return false;
      }

      var responseContent = await response.Content.ReadAsStringAsync();
      var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, new JsonSerializerOptions
      {
        PropertyNameCaseInsensitive = true
      });

      if (authResponse?.Status != true || string.IsNullOrEmpty(authResponse.UserToken))
      {
        _logger.LogError("Authentication response did not contain a valid token");
        return false;
      }

      _authToken = authResponse.UserToken;
      _tokenExpiry = DateTime.UtcNow.AddHours(1); // Token is valid for 1 hour according to the API docs

      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Exception occurred during authentication with the Auction API");
      return false;
    }
  }

  public async Task<string> GetVehicleInspectionsHtmlAsync(string inventVehicleId)
  {
    try
    {
      var url = $"https://backoffice.cityauctiongroup.com/admin/motorvehicleinspections/default/{inventVehicleId}";

      var request = new HttpRequestMessage(HttpMethod.Post, url);

      // Add HTML-specific headers
      request.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
      request.Headers.Add("Accept-Language", "en-GB,en-US;q=0.9,en;q=0.8");

      // Add cookies using Cookie header
      var cookies = new Dictionary<string, string>
        {
            { "username", _configuration.CookieUserName },
            { "usertoken", _configuration.CookieToken },
        };

      var cookieHeader = string.Join("; ", cookies.Select(kvp => $"{kvp.Key}={kvp.Value}"));
      request.Headers.Add("Cookie", cookieHeader);

      var response = await _httpClient.SendAsync(request);

      if (response.IsSuccessStatusCode)
      {
        var result = await response.Content.ReadAsStringAsync();



        return result;
      }
      else
      {
        _logger.LogError($"Failed to fetch vehicle HTML for {inventVehicleId}. Status: {response.StatusCode}");
        return null;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error fetching vehicle HTML for {inventVehicleId}");
      return null;
    }
  }

  public async Task<string> GetMotorVehicleInformationHtmlAsync(string inventVehicleId)
  {
    try
    {
      var url = $"https://backoffice.cityauctiongroup.com/admin/auction/motorvehicles/motorvehicle/{inventVehicleId}";

      var request = new HttpRequestMessage(HttpMethod.Post, url);

      // Add HTML-specific headers
      request.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
      request.Headers.Add("Accept-Language", "en-GB,en-US;q=0.9,en;q=0.8");

      // Add cookies using Cookie header
      var cookies = new Dictionary<string, string>
        {
            { "username", _configuration.CookieUserName },
            { "usertoken", _configuration.CookieToken },
        };

      var cookieHeader = string.Join("; ", cookies.Select(kvp => $"{kvp.Key}={kvp.Value}"));
      request.Headers.Add("Cookie", cookieHeader);

      var response = await _httpClient.SendAsync(request);

      if (response.IsSuccessStatusCode)
      {
        var result = await response.Content.ReadAsStringAsync();

        return result;
      }
      else
      {
        _logger.LogError($"Failed to fetch vehicle HTML for {inventVehicleId}. Status: {response.StatusCode}");
        return null;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error fetching vehicle HTML for {inventVehicleId}");
      return null;
    }
  }


  public async Task<string> GetInspectionIdFromHtmlAsync(string html)
  {
    // Find all NAMA Condition Check rows and extract their inspection IDs
    var pattern = @"<td>NAMA Condition Check</td>.*?motorvehicleinspection/\d+/(\d+)\?";
    var matches = Regex.Matches(html, pattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

    if (matches.Count > 0)
    {
      // Return the first match (latest since they're in date order)
      return matches[0].Groups[1].Value;
    }

    Console.WriteLine("No NAMA Condition Check inspections found.");
    return "";
  }

  public async Task<List<string>> GetFeaturesFromHtmlAsync(string html)
  {
    var context = BrowsingContext.New(Configuration.Default);
    var document = await context.OpenAsync(req => req.Content(html));

    // Extract paintwork features
    var paintwork = ExtractFeatures(document, "Entertainment");

    // Extract driver convenience features
    var driverConvenience = ExtractFeatures(document, "Driver Convenience");

    // Extract entertainment features
    var entertainment = ExtractFeatures(document, "Entertainment");

    paintwork.AddRange(driverConvenience);
    paintwork.AddRange(entertainment);

    return paintwork;
  }

  public async Task<int?> GetBuyNowFromHtmlAsync(string html)
  {
    var config = Configuration.Default;
    var context = BrowsingContext.New(config);
    var document = await context.OpenAsync(req => req.Content(html));

    var input = document.QuerySelector("input#form_motorvehicle_price");

    if (input != null)
    {
      var valueString = input.GetAttribute("value")?.Replace(",", "");
      if (decimal.TryParse(valueString, out decimal decimalPrice))
      {
        int buyNowPrice = (int)Math.Round(decimalPrice); // if you want to round

        return buyNowPrice;
      }
    }

    return null;
  }

  static List<string> ExtractFeatures(IDocument document, string sectionTitle)
  {
    // Locate the heading for the section
    var sectionHeader = document.QuerySelectorAll("h3, h4")
                                .FirstOrDefault(h => h.TextContent.Trim().Equals(sectionTitle, StringComparison.OrdinalIgnoreCase));

    if (sectionHeader == null)
      return new List<string>();

    // The features are typically buttons following the section heading
    var features = new List<string>();
    var featureButtons = sectionHeader.ParentElement
                                      .QuerySelectorAll("button.btn.btn-success");

    foreach (var button in featureButtons)
    {
      var featureName = button.TextContent.Trim();
      if (!string.IsNullOrWhiteSpace(featureName))
      {
        features.Add(featureName);
      }
    }

    return features;
  }

  private async Task<List<InventServiceHistoryItem>> GetServiceHistoryFromHtmlAsync(string html)
  {
    var serviceHistoryItems = new List<InventServiceHistoryItem>();

    try
    {
      var config = Configuration.Default;
      var context = BrowsingContext.New(config);
      var document = await context.OpenAsync(req => req.Content(html));

      // Look for the service history panel/section
      var serviceHistorySection = document.QuerySelector(".dafieldgroup[data-groupname='servicehistory']");

      if (serviceHistorySection != null)
      {
        // Find all service history rows within the panel
        var serviceRows = serviceHistorySection.QuerySelectorAll(".dafieldgroupitems .panel");

        foreach (var row in serviceRows)
        {
          var serviceItem = new InventServiceHistoryItem();

          // Extract mileage
          var mileageInput = row.QuerySelector("input[name*='servicehistory_mileage']");
          if (mileageInput != null && uint.TryParse(mileageInput.GetAttribute("value")?.Replace(",", ""), out uint mileage))
          {
            serviceItem.Odometer = mileage;
          }

          // Extract service date
          var dateInput = row.QuerySelector("input[name*='servicehistory_date']");
          if (dateInput != null)
          {
            var dateValue = dateInput.GetAttribute("value");
            if (!string.IsNullOrEmpty(dateValue) && DateTime.TryParseExact(dateValue, "dd/MM/yyyy",
                CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime serviceDate))
            {
              serviceItem.ServiceDate = serviceDate;
            }
          }

          // Extract garage name
          var garageNameInput = row.QuerySelector("input[name*='servicehistory_garage_name']");
          if (garageNameInput != null)
          {
            serviceItem.DealerName = garageNameInput.GetAttribute("value")?.Trim();
          }

          // Extract garage/dealer type
          var garageTypeSelect = row.QuerySelector("select[name*='servicehistory_type'] option[selected]");
          if (garageTypeSelect != null)
          {
            serviceItem.DealerType = garageTypeSelect.GetAttribute("value")?.Trim();
          }

          // Extract documentation type
          var documentationSelect = row.QuerySelector("select[name*='servicehistory_documentation'] option[selected]");
          if (documentationSelect != null)
          {
            serviceItem.Documentation = documentationSelect.GetAttribute("value")?.Trim();
          }

          // Only add if we have meaningful data
          if (serviceItem.ServiceDate != default(DateTime) ||
              serviceItem.Odometer > 0 ||
              !string.IsNullOrEmpty(serviceItem.DealerName))
          {
            serviceHistoryItems.Add(serviceItem);
          }
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error extracting service history from HTML");
    }

    return serviceHistoryItems.OrderBy(s => s.ServiceDate).ToList();
  }

  public async Task<InventInspectionResult> GetInspectionResultFromHtmlAsync(string html)
  {
    var result = new InventInspectionResult
    {
      DamageItems = new List<InventDamageItem>(),
      TyreItems = new List<InventTyreItem>(),
      InspectionDate = DateTime.MinValue
    };

    // Extract inspection date
    var datePattern = @"name=""inspection_datetime""\s+value=""([^""]+)""";
    var dateMatch = Regex.Match(html, datePattern, RegexOptions.IgnoreCase);
    if (dateMatch.Success)
    {
      var dateString = dateMatch.Groups[1].Value.Trim();
      if (DateTime.TryParseExact(dateString, "dd/MM/yyyy HH:mm:ss",
          CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
      {
        result.InspectionDate = parsedDate;
      }
    }

    // Extract damage items (your existing code)
    var damagePattern = @"<tr>\s*<td>([^<]*)</td>\s*<td>([^<]*)</td>\s*<td>([^<]*)</td>\s*<td[^>]*>(?:.*?href=""([^""]+)"".*?)?</td>\s*</tr>";
    var damageMatches = Regex.Matches(html, damagePattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

    foreach (Match match in damageMatches)
    {
      var component = match.Groups[1].Value.Trim();
      var condition = match.Groups[2].Value.Trim();
      var severity = match.Groups[3].Value.Trim();
      var imageUrl = match.Groups[4].Value.Trim();

      // Skip empty rows or header rows
      if (string.IsNullOrEmpty(component) && string.IsNullOrEmpty(condition) && string.IsNullOrEmpty(severity))
        continue;
      if (component.Equals("Component", StringComparison.OrdinalIgnoreCase))
        continue;

      // Extract filename from URL if available
      string imageFileName = "";
      if (!string.IsNullOrEmpty(imageUrl))
      {
        var lastSlashIndex = imageUrl.LastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < imageUrl.Length - 1)
        {
          imageFileName = imageUrl.Substring(lastSlashIndex + 1);
        }
      }

      // Only add if we have actual damage data
      if (!string.IsNullOrEmpty(component) || !string.IsNullOrEmpty(condition) || !string.IsNullOrEmpty(severity))
      {
        result.DamageItems.Add(new InventDamageItem
        {
          Component = component,
          Condition = condition,
          Severity = severity,
          ImageUrl = imageUrl,
          ImageFileName = imageFileName
        });
      }
    }

    // Extract tyre information
    // Find the tyres table section
    var tyresSectionPattern = @"<h3>Tyres</h3>\s*<table[^>]*>.*?</table>";
    var tyresSectionMatch = Regex.Match(html, tyresSectionPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

    if (tyresSectionMatch.Success)
    {
      var tyresTableHtml = tyresSectionMatch.Value;

      // Extract tyre rows (skip header row)
      var tyreRowPattern = @"<tr>\s*<td>([^<]*)</td>\s*<td>([^<]*)</td>\s*<td>([^<]*)</td>\s*</tr>";
      var tyreMatches = Regex.Matches(tyresTableHtml, tyreRowPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

      foreach (Match match in tyreMatches)
      {
        var position = match.Groups[1].Value.Trim();
        var make = match.Groups[2].Value.Trim();
        var condition = match.Groups[3].Value.Trim();

        // Skip header row
        if (position.Equals("Tyre", StringComparison.OrdinalIgnoreCase) ||
            position.Equals("Position", StringComparison.OrdinalIgnoreCase))
          continue;

        // Only add if we have actual tyre data
        if (!string.IsNullOrEmpty(position) || !string.IsNullOrEmpty(make) || !string.IsNullOrEmpty(condition))
        {
          result.TyreItems.Add(new InventTyreItem
          {
            Position = position,
            Make = make,
            Condition = condition
          });
        }
      }
    }

    // Extract interior information as notes string
    var interiorSectionPattern = @"<h3>Interior</h3>\s*<table[^>]*>(.*?)</table>";
    var interiorSectionMatch = Regex.Match(html, interiorSectionPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

    if (interiorSectionMatch.Success)
    {
      var interiorTableHtml = interiorSectionMatch.Groups[1].Value;

      // Extract interior rows
      var interiorRowPattern = @"<tr[^>]*>\s*<td>([^<]*)</td>\s*<td>([^<]*)</td>\s*</tr>";
      var interiorMatches = Regex.Matches(interiorTableHtml, interiorRowPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

      var interiorNotes = new List<string>();

      foreach (Match match in interiorMatches)
      {
        var item = match.Groups[1].Value.Trim();
        var condition = match.Groups[2].Value.Trim();

        if (!string.IsNullOrEmpty(item) && !string.IsNullOrEmpty(condition))
        {
          interiorNotes.Add($"{item}: {condition}");
        }
      }

      result.InteriorNotes = string.Join($" - ", interiorNotes);
    }

    return result;
  }

  public async Task<InventServiceHistoryResult> GetServiceHistoryFromHTML(string html)
  {
    var result = new InventServiceHistoryResult
    {
      ServiceHistoryItems = new List<InventServiceHistoryItem>()
    };

    // Extract main service history information
    await ExtractMainServiceHistoryFields(html, result);

    // Extract individual service history records
    result.ServiceHistoryItems = await GetServiceHistoryFromHtmlAsync(html);

    return result;
  }

  private async Task ExtractMainServiceHistoryFields(string html, InventServiceHistoryResult result)
  {
    try
    {
      var config = Configuration.Default;
      var context = BrowsingContext.New(config);
      var document = await context.OpenAsync(req => req.Content(html));

      // Service History Status
      var statusSelect = document.QuerySelector("select[name='motorvehicle_servicehistorystatus'] option[selected]");
      result.ServiceHistoryStatus = statusSelect?.GetAttribute("value");

      // Service History Documentation
      var docSelect = document.QuerySelector("select[name='motorvehicle_servicehistorydocumentation'] option[selected]");
      result.ServiceHistoryDocumentation = docSelect?.GetAttribute("value");

      // Number of service stamps
      var stampsInput = document.QuerySelector("input[name='motorvehicle_servicebooknumstamps']");
      if (int.TryParse(stampsInput?.GetAttribute("value"), out int stamps))
      {
        result.ServiceBookNumStamps = stamps;
      }

      // Last service date
      var lastServiceInput = document.QuerySelector("input[name='motorvehicle_lastservicedate']");
      var lastServiceValue = lastServiceInput?.GetAttribute("value");
      if (!string.IsNullOrEmpty(lastServiceValue) && DateTime.TryParseExact(lastServiceValue, "yyyy-MM-dd",
          CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime lastServiceDate))
      {
        result.LastServiceDate = lastServiceDate;
      }

      // Last service miles
      var lastServiceMilesInput = document.QuerySelector("input[name='motorvehicle_lastservicemiles']");
      var lastServiceMilesValue = lastServiceMilesInput?.GetAttribute("value")?.Replace(",", "");
      if (uint.TryParse(lastServiceMilesValue, out uint lastServiceMiles))
      {
        result.LastServiceMiles = lastServiceMiles;
      }

      // Service interval miles
      var intervalMilesInput = document.QuerySelector("input[name='motorvehicle_serviceintervalmiles']");
      if (uint.TryParse(intervalMilesInput?.GetAttribute("value"), out uint intervalMiles))
      {
        result.ServiceIntervalMiles = intervalMiles;
      }

      // Service interval months
      var intervalMonthsInput = document.QuerySelector("input[name='motorvehicle_serviceintervalmonths']");
      if (uint.TryParse(intervalMonthsInput?.GetAttribute("value"), out uint intervalMonths))
      {
        result.ServiceIntervalMonths = intervalMonths;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error extracting main service history fields");
    }
  }


  public async Task<string> GetInspectionHtmlAsync(string inventVehicleId, string inspectionId)
  {
    try
    {
      var vehicleId = inventVehicleId;
      var url = $"https://backoffice.cityauctiongroup.com/admin/motorvehicleinspections/motorvehicleinspection/{inventVehicleId}/{inspectionId}";

      var request = new HttpRequestMessage(HttpMethod.Get, url);

      // Add HTML-specific headers
      request.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
      request.Headers.Add("Accept-Language", "en-GB,en-US;q=0.9,en;q=0.8");

      // Add cookies using Cookie header
      var cookies = new Dictionary<string, string>
        {
            { "username", _configuration.CookieUserName },
            { "usertoken", _configuration.CookieToken },
        };

      var cookieHeader = string.Join("; ", cookies.Select(kvp => $"{kvp.Key}={kvp.Value}"));
      request.Headers.Add("Cookie", cookieHeader);

      var response = await _httpClient.SendAsync(request);

      if (response.IsSuccessStatusCode)
      {
        var result = await response.Content.ReadAsStringAsync();



        return result;
      }
      else
      {
        _logger.LogError($"Failed to fetch vehicle HTML for {inventVehicleId}. Status: {response.StatusCode}");
        return null;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error fetching vehicle HTML for {inventVehicleId}");
      return null;
    }
  }

  public static string CustomBase32(string inputString)
  {
    // Hash input with MD5
    using (MD5 md5 = MD5.Create())
    {
      byte[] hashed = md5.ComputeHash(Encoding.UTF8.GetBytes(inputString));

      // Encode in Base32, lowercase, no padding
      return ToBase32(hashed).ToLower().TrimEnd('=');
    }
  }

  private static string ToBase32(byte[] input)
  {
    const string base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
    StringBuilder result = new StringBuilder();

    int buffer = 0;
    int bitsLeft = 0;

    foreach (byte b in input)
    {
      buffer = (buffer << 8) | b;
      bitsLeft += 8;

      while (bitsLeft >= 5)
      {
        result.Append(base32Chars[(buffer >> (bitsLeft - 5)) & 0x1F]);
        bitsLeft -= 5;
      }
    }

    if (bitsLeft > 0)
    {
      result.Append(base32Chars[(buffer << (5 - bitsLeft)) & 0x1F]);
    }

    return result.ToString();
  }

  //private string CustomBase32(string inputString)
  //{
  //  // Hash input with MD5
  //  using (MD5 md5 = MD5.Create())
  //  {
  //    byte[] hashed = md5.ComputeHash(Encoding.UTF8.GetBytes(inputString));

  //    // Convert to Base32 (lowercase, no padding)
  //    return ToBase32(hashed).ToLower().TrimEnd('=');
  //  }
  //}

  /// <summary>
  /// Gets a list of upcoming auctions
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of upcoming auctions</returns>
  public async Task<List<Auction>> GetUpcomingAuctionsAsync(CancellationToken cancellationToken = default)
  {
    await EnsureAuthenticatedAsync(cancellationToken);

    var response = await _httpClient.GetAsync("/api/auction/2.0/auctions", cancellationToken);

    if (!response.IsSuccessStatusCode)
    {
      _logger.LogError($"Failed to get upcoming auctions: {response.StatusCode}");
      return new List<Auction>();
    }

    var responseContent = await response.Content.ReadAsStringAsync();
    var auctionsResponse = JsonSerializer.Deserialize<AuctionsResponse>(responseContent, _jsonOptions);
    if (auctionsResponse?.Status != true || auctionsResponse.Auctions == null)
    {
      _logger.LogError("Auctions response was invalid");
      return new List<Auction>();
    }

    return auctionsResponse.Auctions;
  }

  /// <summary>
  /// Gets details for a specific auction by ID
  /// </summary>
  /// <param name="auctionId">ID of the auction</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Auction details</returns>
  private async Task<Auction> GetAuctionByIdAsync(string auctionId, CancellationToken cancellationToken = default)
  {
    await EnsureAuthenticatedAsync(cancellationToken);

    var response = await _httpClient.GetAsync($"/api/auction/2.0/auction/{auctionId}", cancellationToken);

    if (!response.IsSuccessStatusCode)
    {
      _logger.LogError($"Failed to get auction {auctionId}: {response.StatusCode}");
      return null;
    }

    var responseContent = await response.Content.ReadAsStringAsync();
    var auctionResponse = JsonSerializer.Deserialize<AuctionResponse>(responseContent, _jsonOptions);

    if (auctionResponse?.Status != true || auctionResponse.Auction == null)
    {
      _logger.LogError($"Auction response for {auctionId} was invalid");
      return null;
    }

    return auctionResponse.Auction;
  }

  public async Task<InventAuction> FetchAuctionWithLotsAsync(string auctionId, CancellationToken cancellationToken = default)
  {
    var result = new InventAuction();

    // Get all upcoming auctions
    var apiAuction = await GetAuctionByIdAsync(auctionId, cancellationToken);
    if (apiAuction == null)
    {
      _logger.LogWarning($"No auction found for id {apiAuction}");
      return result;
    }

    try
    {
      // Map to our entity
      var inventAuction = MapApiAuctionToInventAuction(apiAuction);

      // Get lots for this auction
      var lots = await GetLotsByAuctionIdAsync(auctionId, cancellationToken: cancellationToken);
      if (lots == null || lots.Count == 0)
      {
        _logger.LogInformation($"No lots found for auction {apiAuction.AuctionId}");
        return result;
      }

      _logger.LogInformation($"Found {lots.Count} lots for auction {apiAuction.AuctionId}");

      // Map each lot to our entity
      var inventLots = new List<InventAuctionLot>();
      foreach (var lot in lots)
      {
        var inventLot = MapApiLotToInventLot(lot);
        inventLot.AuctionId = inventAuction.Id;
        inventLots.Add(inventLot);
      }

      result.ActivityStatus = inventAuction.ActivityStatus;
      result.StatusId = inventAuction.StatusId;
      result.Added = inventAuction.Added;
      result.Updated = inventAuction.Updated;
      result.AllowStandOn = inventAuction.AllowStandOn;
      result.AuctionId = inventAuction.AuctionId;
      result.AuctionLocationId = inventAuction.AuctionLocationId;
      result.AuctionLocationTitle = inventAuction.AuctionLocationTitle;
      result.AuctionTypeTitle = inventAuction.AuctionTypeTitle;
      result.CurrentLotId = inventAuction.CurrentLotId;
      result.DateTime = inventAuction.DateTime;
      result.EndDateTime = inventAuction.EndDateTime;
      result.Information = inventAuction.Information;
      result.Title = inventAuction.Title;
      result.Lots = inventLots;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error processing auction {apiAuction.AuctionId}");
    }

    return result;
  }

  /// <summary>
  /// Fetches all auctions and their lots one by one
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Dictionary of auctions with their corresponding lots</returns>
  public async Task<Dictionary<InventAuction, List<InventAuctionLot>>> FetchAllAuctionsWithLotsAsync(CancellationToken cancellationToken = default)
  {
    var result = new Dictionary<InventAuction, List<InventAuctionLot>>();

    // Get all upcoming auctions
    var auctions = await GetUpcomingAuctionsAsync(cancellationToken);
    if (auctions == null || auctions.Count == 0)
    {
      _logger.LogWarning("No upcoming auctions found");
      return result;
    }

    _logger.LogInformation($"Found {auctions.Count} upcoming auctions to process");

    // Process each auction one by one
    foreach (var apiAuction in auctions)
    {
      try
      {
        _logger.LogInformation($"Processing auction {apiAuction.AuctionId}");

        // Get full auction details
        var auctionDetails = await GetAuctionByIdAsync(apiAuction.AuctionId, cancellationToken);
        if (auctionDetails == null)
        {
          _logger.LogWarning($"Could not retrieve details for auction {apiAuction.AuctionId}, skipping");
          continue;
        }

        // Map to our entity
        var inventAuction = MapApiAuctionToInventAuction(apiAuction);

        // Get lots for this auction
        var lots = await GetLotsByAuctionIdAsync(apiAuction.AuctionId, cancellationToken: cancellationToken);
        if (lots == null || lots.Count == 0)
        {
          _logger.LogInformation($"No lots found for auction {apiAuction.AuctionId}");
          result.Add(inventAuction, new List<InventAuctionLot>());
          continue;
        }

        _logger.LogInformation($"Found {lots.Count} lots for auction {apiAuction.AuctionId}");

        // Map each lot to our entity
        var inventLots = new List<InventAuctionLot>();
        foreach (var lot in lots)
        {
          var inventLot = MapApiLotToInventLot(lot);
          inventLot.AuctionId = inventAuction.Id;
          inventLots.Add(inventLot);
        }

        result.Add(inventAuction, inventLots);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error processing auction {apiAuction.AuctionId}");
      }
    }

    return result;
  }

  /// <summary>
  /// Gets lots for all upcoming auctions - now delegates to FetchAllAuctionsWithLotsAsync
  /// </summary>
  /// <param name="pageSize">Number of lots per page</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of lots</returns>
  public async Task<List<AuctionLot>> GetAllLotsAsync(int pageSize = 100, CancellationToken cancellationToken = default)
  {
    _logger.LogInformation("Getting all lots across all auctions");

    var allLots = new List<AuctionLot>();

    // Get all upcoming auctions
    var auctions = await GetUpcomingAuctionsAsync(cancellationToken);
    if (auctions == null || auctions.Count == 0)
    {
      _logger.LogWarning("No upcoming auctions found");
      return allLots;
    }

    // Process each auction one by one
    foreach (var auction in auctions)
    {
      try
      {
        var auctionLots = await GetLotsByAuctionIdAsync(auction.AuctionId, pageSize, cancellationToken);
        if (auctionLots != null && auctionLots.Count > 0)
        {
          _logger.LogInformation($"Adding {auctionLots.Count} lots from auction {auction.AuctionId}");
          allLots.AddRange(auctionLots);
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error getting lots for auction {auction.AuctionId}");
      }
    }

    _logger.LogInformation($"Retrieved a total of {allLots.Count} lots from all auctions");
    return allLots;
  }

  /// <summary>
  /// Gets lots for a specific auction
  /// </summary>
  /// <param name="auctionId">ID of the auction</param>
  /// <param name="pageSize">Number of lots per page</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of lots</returns>
  public async Task<List<AuctionLot>> GetLotsByAuctionIdAsync(string auctionId, int pageSize = 100, CancellationToken cancellationToken = default)
  {
    await EnsureAuthenticatedAsync(cancellationToken);

    var allLots = new List<AuctionLot>();
    var currentPage = 1;
    bool hasMorePages = true;

    while (hasMorePages)
    {
      var response = await _httpClient.GetAsync($"/api/auction/2.0/lots/{auctionId}?page={currentPage}&pagesize={pageSize}", cancellationToken);

      if (!response.IsSuccessStatusCode)
      {
        _logger.LogError($"Failed to get lots for auction {auctionId} page {currentPage}: {response.StatusCode}");
        break;
      }

      var responseContent = await response.Content.ReadAsStringAsync();
      var lotsResponse = JsonSerializer.Deserialize<LotsResponse>(responseContent, _jsonOptions);

      if (lotsResponse?.Status != true || lotsResponse.Lots == null)
      {
        _logger.LogError($"Lots response for auction {auctionId} page {currentPage} was invalid");
        break;
      }

      allLots.AddRange(lotsResponse.Lots);

      // Check if we have more pages to fetch
      if (lotsResponse.Pagination != null)
      {
        int totalPages = (int)Math.Ceiling((double)lotsResponse.Pagination.Total / lotsResponse.Pagination.PageSize);
        hasMorePages = currentPage < totalPages;
        currentPage++;

        // Safety check for empty results
        if (lotsResponse.Lots.Count == 0)
        {
          hasMorePages = false;
        }
      }
      else
      {
        hasMorePages = false;
      }
    }

    return allLots;
  }

  /// <summary>
  /// Ensures the client is authenticated before making API calls
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  private async Task EnsureAuthenticatedAsync(CancellationToken cancellationToken)
  {
    if (string.IsNullOrEmpty(_authToken) || _tokenExpiry <= DateTime.UtcNow)
    {
      bool authenticated = await AuthenticateAsync(cancellationToken);
      if (!authenticated)
      {
        throw new InvalidOperationException("Failed to authenticate with the Auction API");
      }
    }

    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _authToken);
  }

  private InventAuction MapApiAuctionToInventAuction(Auction apiAuction)
  {
    if (apiAuction == null)
      return null;

    var auction = new InventAuction
    {
      AuctionId = long.TryParse(apiAuction.Id, out long auctionId) ? auctionId : 0,
      Title = apiAuction.Title,
      DateTime = apiAuction.StartDate ??
                   (string.IsNullOrEmpty(apiAuction.DateTime) ? null :
                   DateTime.TryParse(apiAuction.DateTime, out DateTime dt) ? dt : null),
      EndDateTime = apiAuction.EndDate ??
                     (string.IsNullOrEmpty(apiAuction.EndDateTime) ? null :
                     DateTime.TryParse(apiAuction.EndDateTime, out DateTime edt) ? edt : null),
      ActivityStatus = int.TryParse(
            !string.IsNullOrEmpty(apiAuction.ActivityStatus) ?
            apiAuction.ActivityStatus : apiAuction.Status,
            out int activityStatus) ? activityStatus : 1,
      AllowStandOn = !string.IsNullOrEmpty(apiAuction.AllowStandOn) &&
            (apiAuction.AllowStandOn.Equals("true", StringComparison.OrdinalIgnoreCase) ||
             apiAuction.AllowStandOn.Equals("1")),
      CurrentLotId = int.TryParse(apiAuction.CurrentLot, out int currentLotId) ?
            currentLotId : null,
      Information = apiAuction.Information,
      AuctionLocationId = int.TryParse(apiAuction.AuctionLocationId, out int locationId) ?
            locationId : 0,
      AuctionLocationTitle = apiAuction.AuctionLocationTitle,
      AuctionTypeTitle = apiAuction.AuctionTypeTitle,
      Lots = new List<InventAuctionLot>(),
      StatusId = (uint)StatusEnum.Active,
      Added = DateTime.Now,
      Updated = DateTime.Now
    };

    auction.Id = Guid.NewGuid();

    return auction;
  }

  private InventAuctionLot MapApiLotToInventLot(AuctionLot apiLot)
  {
    if (apiLot == null)
      return null;

    var lot = new InventAuctionLot
    {
      // Use LotId from API
      LotId = long.TryParse(apiLot.LotId, out long lotId) ? lotId : 0,

      InventVehicleId = !string.IsNullOrEmpty(apiLot.VehicleId) &&
                    long.TryParse(apiLot.VehicleId, out long vehicleId) ?
                    vehicleId : (long?)null,

      // LotNumber should come from full details if available
      LotNumber = apiLot.Full?.AuctionLotNumber != null &&
                    int.TryParse(apiLot.Full.AuctionLotNumber, out int lotNumber) ?
                    lotNumber : null,

      // Default status to 1 (Selling) if not provided
      Status = !string.IsNullOrEmpty(apiLot.AuctionLotStatus) &&
                 int.TryParse(apiLot.AuctionLotStatus, out int status) ?
                 status : 1,

      Type = "motorvehicle",

      Description = !string.IsNullOrEmpty(apiLot.Variant) ?
              $"{apiLot.Manufacturer} {apiLot.Model} {apiLot.Variant} {apiLot.Colour}" :
              $"{apiLot.Manufacturer} {apiLot.Model} {apiLot.Colour}",

      // Basic vehicle information
      Vrm = apiLot.Vrm,
      Vin = apiLot.Vin,
      Manufacturer = apiLot.Manufacturer,
      Model = apiLot.Model,
      Variant = apiLot.Variant,

      Year = !string.IsNullOrEmpty(apiLot.Year) &&
               int.TryParse(apiLot.Year, out int year) ?
               year : null,

      Mileage = apiLot.Mileage,
      MileageDenominator = apiLot.Full?.MileageDenominator ?? "Miles",

      // Get MileageWarranted from Full details
      MileageWarranted = apiLot.Full?.MileageWarranted == "1" ||
                           apiLot.Full?.MileageWarranted?.Equals("true", StringComparison.OrdinalIgnoreCase) == true,

      FirstRegistered = !string.IsNullOrEmpty(apiLot.FirstRegDate) &&
              DateTime.TryParse(apiLot.FirstRegDate, out DateTime regDate) ?
              regDate : null,

      Colour = apiLot.Colour,
      FuelType = apiLot.FuelType,
      EngineSize = apiLot.EngineSize,
      BodyType = apiLot.BodyType,
      DoorCount = apiLot.DoorCount,
      Transmission = apiLot.Transmission,

      // Parse FormerKeeper from Full details
      FormerKeeper = apiLot.Full?.FormerKeepers != null &&
                       int.TryParse(apiLot.Full.FormerKeepers, out int formerKeepers) ?
                       formerKeepers : null,

      NamaGrade = apiLot.NamaGrade,

      // Parse CAP values - they come as strings but need to be decimals
      CapRetail = !string.IsNullOrEmpty(apiLot.CapRetail) &&
                    decimal.TryParse(apiLot.CapRetail, out decimal capRetail) ?
                    capRetail : null,

      CapClean = !string.IsNullOrEmpty(apiLot.CapClean) &&
                   decimal.TryParse(apiLot.CapClean, out decimal capClean) ?
                   capClean : null,

      CapAverage = !string.IsNullOrEmpty(apiLot.CapAverage) &&
                     decimal.TryParse(apiLot.CapAverage, out decimal capAverage) ?
                     capAverage : null,

      CapBelow = !string.IsNullOrEmpty(apiLot.CapBelow) &&
                   decimal.TryParse(apiLot.CapBelow, out decimal capBelow) ?
                   capBelow : null,

      // AutoTrader values
      AutotraderRetail = !string.IsNullOrEmpty(apiLot.AutotraderRetail) &&
                           decimal.TryParse(apiLot.AutotraderRetail, out decimal autoRetail) ?
                           autoRetail : null,

      AutotraderTrade = !string.IsNullOrEmpty(apiLot.AutotraderTrade) &&
                          decimal.TryParse(apiLot.AutotraderTrade, out decimal autoTrade) ?
                          autoTrade : null,

      // Classification and status
      ClassificationId = apiLot.ClassificationId,
      ClassificationTitle = apiLot.ClassificationTitle,
      VatStatus = apiLot.VatStatus,
      HasV5 = apiLot.HasV5,
      V5OrderStatus = apiLot.V5OrderStatus,
      ServiceHistory = apiLot.ServiceHistory,

      // Parse NonRunner - check both API level and Full details
      NonRunner = (!string.IsNullOrEmpty(apiLot.NonRunner) &&
                    (apiLot.NonRunner == "1" || apiLot.NonRunner.Equals("true", StringComparison.OrdinalIgnoreCase))),

      InspectionReportUrl = apiLot.InspectionReport,

      //// Auction information
      //AuctionId = !string.IsNullOrEmpty(apiLot.AuctionId) &&
      //              long.TryParse(apiLot.AuctionId, out long auctionId) ?
      //              auctionId : (long?)null,

      //AuctionDateTime = !string.IsNullOrEmpty(apiLot.AuctionDateTime) &&
      //                    DateTime.TryParse(apiLot.AuctionDateTime, out DateTime auctionDateTime) ?
      //                    auctionDateTime : (DateTime?)null,

      //AuctionTypeTitle = apiLot.AuctionTypeTitle,
      //AuctionLocationId = apiLot.AuctionLocationId,
      //AuctionLocationTitle = apiLot.AuctionLocationTitle,
      //LogisticsLocationId = apiLot.LogisticsLocationId,

      //// Vendor information
      //VendorName = apiLot.VendorName,
      //VendorAlias = apiLot.VendorAlias, // Add this property to AuctionLot if missing

      // Get CO2 from either level
      Co2 = !string.IsNullOrEmpty(apiLot.Co2) ? apiLot.Co2 : apiLot.Full?.Co2,

      StandardEuroEmissions = apiLot.Full?.StandardEuroEmissions,
      NumKeys = apiLot.Full?.NumKeys,
      ReservePrice = apiLot.ReservePrice,
      CurrentBid = apiLot.CurrentBid,
      SalePrice = null, // This would come from bidding/sale data
      SoldToCustomerId = null, // This would come from bidding/sale data

      // Initialize empty images collection - will be populated below
      Images = new List<InventAuctionLotImage>(),
      StatusId = (uint)StatusEnum.Active,
      Added = DateTime.Now,
      Updated = DateTime.Now
    };

    // Enhanced Full details processing
    //if (apiLot.Full != null)
    //{
    //  // Vehicle identification
    //  lot.PrevRegistration = apiLot.Full.PrevRegistration;

    //  // MOT and Tax information
    //  if (!string.IsNullOrEmpty(apiLot.Full.MotExpires) &&
    //      DateTime.TryParse(apiLot.Full.MotExpires, out DateTime motExpires))
    //  {
    //    lot.MotExpires = motExpires;
    //  }

    //  if (!string.IsNullOrEmpty(apiLot.Full.TaxDue) &&
    //      DateTime.TryParse(apiLot.Full.TaxDue, out DateTime taxDue))
    //  {
    //    lot.TaxDue = taxDue;
    //  }

    //  // Technical specifications
    //  lot.Bhp = apiLot.Full.Bhp;
    //  lot.ExactCc = apiLot.Full.ExactCc;

    //  // Fuel efficiency
    //  lot.MpgUrban = apiLot.Full.MpgUrban;
    //  lot.MpgExtraUrban = apiLot.Full.MpgExtraUrban;
    //  lot.MpgCombined = apiLot.Full.MpgCombined;

    //  // Dimensions
    //  lot.Length = apiLot.Full.Length;
    //  lot.Width = apiLot.Full.Width;
    //  lot.Height = apiLot.Full.Height;
    //  lot.GrossVehicleWeight = apiLot.Full.GrossVehicleWeight;

    //  // Capacity
    //  lot.SeatingCapacity = apiLot.Full.SeatingCapacity;
    //  lot.LuggageCapacitySeatsUp = apiLot.Full.LuggageCapacitySeatsUp;
    //  lot.LuggageCapacitySeatsDown = apiLot.Full.LuggageCapacitySeatsDown;

    //  // Safety ratings
    //  lot.NcapOverallRating = apiLot.Full.NcapOverallRating;
    //  lot.NcapAdultOccupantPercentage = apiLot.Full.NcapAdultOccupantPercentage;
    //  lot.NcapChildOccupantPercentage = apiLot.Full.NcapChildOccupantPercentage;
    //  lot.NcapPedestrianPercentage = apiLot.Full.NcapPedestrianPercentage;
    //  lot.NcapSafetyAssistPercentage = apiLot.Full.NcapSafetyAssistPercentage;

    //  // Additional notes and title
    //  lot.CatalogueNotes = apiLot.Full.CatalogueNotes;
    //  lot.VehicleTitle = apiLot.Full.Title;

    //  // Vehicle flags
    //  if (apiLot.Full.Flags != null)
    //  {
    //    lot.IsFinanced = apiLot.Full.Flags.Finance == 1;
    //    lot.IsImported = apiLot.Full.Flags.Imported == 1;
    //    lot.IsExported = apiLot.Full.Flags.Exported == 1;
    //    lot.IsScrapped = apiLot.Full.Flags.Scrapped == 1;
    //    lot.IsStolen = apiLot.Full.Flags.Stolen == 1;
    //    lot.HasVcarTheft = apiLot.Full.Flags.VcarTheft == 1;
    //    lot.HasVcarDamage = apiLot.Full.Flags.VcarDamaged == 1;
    //  }

    //  // Override with more detailed information from Full if main level is empty
    //  if (string.IsNullOrEmpty(lot.Transmission) && !string.IsNullOrEmpty(apiLot.Full.GearboxType))
    //  {
    //    lot.Transmission = apiLot.Full.GearboxType;
    //  }

    //  // Use Full details for V5 status if main level is false
    //  if (!lot.HasV5 && !string.IsNullOrEmpty(apiLot.Full.HasV5))
    //  {
    //    lot.HasV5 = apiLot.Full.HasV5 == "1" ||
    //        apiLot.Full.HasV5.Equals("true", StringComparison.OrdinalIgnoreCase);
    //  }

    //  // Override CAP values from Full if main level is null
    //  if (lot.CapRetail == null && !string.IsNullOrEmpty(apiLot.Full.CapRetail))
    //  {
    //    lot.CapRetail = decimal.TryParse(apiLot.Full.CapRetail, out decimal fullCapRetail) ?
    //                    fullCapRetail : null;
    //  }

    //  if (lot.CapClean == null && !string.IsNullOrEmpty(apiLot.Full.CapClean))
    //  {
    //    lot.CapClean = decimal.TryParse(apiLot.Full.CapClean, out decimal fullCapClean) ?
    //                   fullCapClean : null;
    //  }

    //  if (lot.CapAverage == null && !string.IsNullOrEmpty(apiLot.Full.CapAverage))
    //  {
    //    lot.CapAverage = decimal.TryParse(apiLot.Full.CapAverage, out decimal fullCapAverage) ?
    //                     fullCapAverage : null;
    //  }

    //  if (lot.CapBelow == null && !string.IsNullOrEmpty(apiLot.Full.CapBelow))
    //  {
    //    lot.CapBelow = decimal.TryParse(apiLot.Full.CapBelow, out decimal fullCapBelow) ?
    //                   fullCapBelow : null;
    //  }
    //}

    // Process all images without duplicates
    var allImageUrls = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    var imageList = new List<InventAuctionLotImage>();

    // Add main images
    if (apiLot.Images != null && apiLot.Images.Any())
    {
      foreach (var imageUrl in apiLot.Images)
      {
        if (!string.IsNullOrWhiteSpace(imageUrl) && allImageUrls.Add(imageUrl))
        {
          imageList.Add(new InventAuctionLotImage
          {
            Id = Guid.NewGuid(),
            ImageUrl = imageUrl,
            IsInterior = false,
            SortOrder = imageList.Count,
            StatusId = (uint)StatusEnum.Active,
            Added = DateTime.Now,
            Updated = DateTime.Now
          });
        }
      }
    }

    // Add interior images from Full.Images if available and different from main images
    if (apiLot.Full?.Images != null && apiLot.Full.Images.Any())
    {
      foreach (var imageUrl in apiLot.Full.Images)
      {
        if (!string.IsNullOrWhiteSpace(imageUrl) && allImageUrls.Add(imageUrl))
        {
          imageList.Add(new InventAuctionLotImage
          {
            Id = Guid.NewGuid(),
            ImageUrl = imageUrl,
            IsInterior = false, // Could be either, you might need logic to determine this
            SortOrder = imageList.Count,
            StatusId = (uint)StatusEnum.Active,
            Added = DateTime.Now,
            Updated = DateTime.Now
          });
        }
      }
    }

    // Add interior images from InteriorImages string (if stored as comma-separated)
    if (!string.IsNullOrEmpty(apiLot.InteriorImages))
    {
      var interiorUrls = apiLot.InteriorImages.Split(',', StringSplitOptions.RemoveEmptyEntries);
      foreach (var imageUrl in interiorUrls)
      {
        var trimmedUrl = imageUrl.Trim();
        if (!string.IsNullOrWhiteSpace(trimmedUrl) && allImageUrls.Add(trimmedUrl))
        {
          imageList.Add(new InventAuctionLotImage
          {
            Id = Guid.NewGuid(),
            ImageUrl = trimmedUrl,
            IsInterior = true,
            SortOrder = imageList.Count,
            StatusId = (uint)StatusEnum.Active,
            Added = DateTime.Now,
            Updated = DateTime.Now
          });
        }
      }
    }

    lot.Images = imageList;

    // Set audit fields
    lot.Id = Guid.NewGuid();
    lot.StatusId = (uint)StatusEnum.Active;
    lot.Added = DateTime.Now;
    lot.Updated = DateTime.Now;

    // Update AuctionLotId in images after lot.Id is set
    if (lot.Images?.Any() == true)
    {
      foreach (var image in lot.Images)
      {
        image.AuctionLotId = lot.Id;
      }
    }

    return lot;
  }
}