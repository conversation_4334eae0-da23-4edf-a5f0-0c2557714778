﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Trading.API.Data;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.Services.InspectCollect.Classes.VehicleLookup;

public class ICAutoTraderSettingsService : ICAutoTraderSettingsInterface
{
  private readonly ILogger<ICAutoTraderSettingsService> _logger;
  private readonly Dictionary<Guid, ICAutoTraderSettingsDTO> _settingsLookup;

  public ICAutoTraderSettingsService(
      IServiceScopeFactory serviceScopeFactory,
      ILogger<ICAutoTraderSettingsService> logger)
  {
    _logger = logger;

    // Load all settings once in constructor
    using var scope = serviceScopeFactory.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<TradingContext>();
    var allSettings = context.ICAutoTraderSettings.ToList();

    _settingsLookup = allSettings.ToDictionary(s => s.ICContainerGroupId, s => new ICAutoTraderSettingsDTO
    {
      ICContainerGroupId = s.ICContainerGroupId,
      DealerId = s.DealerId,
      DealerName = s.DealerName,
      HasExtendedMetricsLicense = s.HasExtendedMetricsLicense
    });

    _logger.LogInformation("Loaded {Count} AutoTrader settings into memory", allSettings.Count);
  }

  public ICAutoTraderSettingsDTO GetSettingsForContainerGroup(
      Guid containerGroupId,
      CancellationToken cancellationToken)
  {
    // Fast in-memory lookup, no database access
    _settingsLookup.TryGetValue(containerGroupId, out var settings);
    return settings;
  }

  public List<ICAutoTraderSettingsDTO> GetAllSettings()
  {
    // Return all cached settings
    return _settingsLookup.Values.ToList();
  }

  public ICAutoTraderSettingsDTO GetDefaultSettings(CancellationToken cancellationToken = default)
  {
    return _settingsLookup.Values.FirstOrDefault() 
           ?? new ICAutoTraderSettingsDTO
           {
             ICContainerGroupId = Guid.Empty,
             DealerId = "619856",
             DealerName = "Default",
             HasExtendedMetricsLicense = false
           };
  }
}