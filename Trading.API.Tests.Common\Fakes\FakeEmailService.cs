﻿using brevo_csharp.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Interfaces;

namespace Trading.API.Tests.Common.Fakes
{
  public class FakeEmailService : IEmailService
  {
    public Task<GetReports> GetDailyEmailStats(DateTime? startDate = null, DateTime? endDate = null, long? days = null, string tag = null)
    {
      throw new NotImplementedException();
    }

    public Task<GetEmailEventReport> GetEmailEventsForAddress(string emailAddress, DateTime? startDate = null, DateTime? endDate = null, long? limit = 100)
    {
      throw new NotImplementedException();
    }

    public Task<GetEmailEventReport> GetEmailsForAdvert(string advertTag, DateTime? startDate = null, DateTime? endDate = null)
    {
      throw new NotImplementedException();
    }

    public Task<decimal?> GetTransactionalClickThroughRate(DateTime? startDate = null, DateTime? endDate = null, string tag = null)
    {
      throw new NotImplementedException();
    }

    public Task<GetEmailEventReport> GetTransactionalEmailEvents(DateTime? startDate = null, DateTime? endDate = null, long? limit = 2500, long? offset = 0, string email = null, long? templateId = null, string messageId = null, string eventType = null)
    {
      throw new NotImplementedException();
    }

    public Task<TransactionalEmailMetricsDTO> GetTransactionalEmailMetrics(DateTime? startDate = null, DateTime? endDate = null, string tag = null)
    {
      throw new NotImplementedException();
    }

    public Task<GetAggregatedReport> GetTransactionalEmailStats(DateTime? startDate = null, DateTime? endDate = null, long? days = null, string tag = null)
    {
      throw new NotImplementedException();
    }

    public Task<bool> SendEmail(SendEmailDTO dto, CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<bool> TestSendEmail(CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<bool> ValidateEmail(string emailAddress)
    {
      throw new NotImplementedException();
    }

    public Task<int> StoreRecentEmailEvents()
    {
      throw new NotImplementedException();
    }
  }
}
