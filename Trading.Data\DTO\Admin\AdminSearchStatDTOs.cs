﻿using DocumentFormat.OpenXml.Bibliography;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Admin;

// Request DTOs
public class AdminCustomerSearchAnalyticsRequestDTO : BaseSearchDTO
{
  public AdminCustomerSearchFiltersDTO Filters { get; set; } = new AdminCustomerSearchFiltersDTO();
}

public class AdminAdvertAnalyticsRequestDTO : BaseSearchDTO
{
  public AdminAdvertNotificationFiltersDTO Filters { get; set; } = new AdminAdvertNotificationFiltersDTO();
}

// Filter DTOs
public class AdminCustomerSearchFiltersDTO : BaseFilterGuid
{
  public string CustomerName { get; set; }
  public int? MaxSavedSearches { get; set; }
  public DateTime? FromDate { get; set; }
  public bool? HasActiveNotifications { get; set; }
  public Guid? AssignedTo { get; set; }

  // Placeholder for future use
  public uint? CustomerStatusId { get; set; }
  public uint? ContactStatusId { get; set; }
}

public class AdminAdvertNotificationFiltersDTO : BaseFilterGuid
{
  public Guid? AdvertId { get; set; }
  public string Vrm { get; set; }
  public DateTime? FromDate { get; set; }
  public DateTime? ToDate { get; set; }
  public Guid? VendorId { get; set; }
  public int? MaxMatchedSavedSearches { get; set; }
  public int? MaxNotifications { get; set; }
  public decimal? MaxNotificationRate { get; set; }

  // Advert Status Filters
  public AdvertStatusEnum? AdvertStatus { get; set; }
  public SoldStatusEnum? SoldStatus { get; set; }
  public uint? StatusId { get; set; }
}

// Main Result DTOs
public class AdminCustomerSearchAnalyticsDTO
{
  public Guid CustomerId { get; set; }
  public string CustomerName { get; set; }
  public string CustomerEmail { get; set; }
  public Guid? AssignedTo { get; set; }
  public string AssignedToName { get; set; }

  // Customer-level aggregated statistics
  public int TotalSavedSearches { get; set; }
  public int ActiveNotificationSearches { get; set; }
  public int TotalMatchingAdverts { get; set; }
  public int TotalAdvertsNotifiedAbout { get; set; }

  // Single clear effectiveness metric
  public decimal NotificationEffectivenessRate => TotalMatchingAdverts > 0 ? Math.Round((decimal)TotalAdvertsNotifiedAbout / TotalMatchingAdverts * 100, 1) : 0;

  // Detailed saved searches
  public List<SavedSearchStatsDTO> SavedSearches { get; set; } = new List<SavedSearchStatsDTO>();
}

public class SavedSearchStatsDTO
{
  public Guid Id { get; set; }
  public string SearchName { get; set; }
  public string ContactEmail { get; set; }
  public string ContactName { get; set; }
  public string SearchPhrase { get; set; }
  public string SearchDescription { get; set; }

  // Settings
  public bool? SendUpdates { get; set; }
  public uint? UpdateFrequency { get; set; }
  public bool IsProfile { get; set; }
  public DateTime? Added { get; set; }
  public DateTime? NotificationTime { get; set; }
  public DateTime? PauseUntil { get; set; }
  public bool IsPaused { get; set; }

  // Simple, clear metrics
  public int MatchingAdvertCount { get; set; }
  public int AdvertsNotifiedAbout { get; set; }

  // Single effectiveness rate
  public decimal NotificationEffectivenessRate => MatchingAdvertCount > 0 ? Math.Round((decimal)AdvertsNotifiedAbout / MatchingAdvertCount * 100, 1) : 0;

  // Simplified advert references
  public List<SimplifiedAdvertReferenceDTO> MatchingAdverts { get; set; } = new List<SimplifiedAdvertReferenceDTO>();
}

public class SimplifiedAdvertReferenceDTO
{
  public Guid AdvertId { get; set; }
  public string Vrm { get; set; }
}

public class AdvertNotificationStatsDTO
{
  public Guid AdvertId { get; set; }
  public string Vrm { get; set; }
  public string Description { get; set; }
  public string CustomerName { get; set; }
  public Guid? CustomerId { get; set; }
  public DateTime? Added { get; set; }
  public DateTime? Updated { get; set; }

  // Advert Status Properties
  public AdvertStatusEnum? AdvertStatus { get; set; }
  public SoldStatusEnum? SoldStatus { get; set; }
  public uint? StatusId { get; set; }
  public string StatusName { get; set; }

  // Search matching statistics
  public int MatchedInSavedSearchCount { get; set; }
  public int MatchedInUnsavedSearchCount { get; set; }
  public int TotalSearchMatches => MatchedInSavedSearchCount + MatchedInUnsavedSearchCount;

  // Simple notification metric
  public int ContactsNotifiedAbout { get; set; }

  // Single clear effectiveness rate
  public decimal NotificationEffectivenessRate => MatchedInSavedSearchCount > 0 ? Math.Round((decimal)ContactsNotifiedAbout / MatchedInSavedSearchCount * 100, 1) : 0;

  // Effectiveness indicators
  public string NotificationEffectiveness
  {
    get
    {
      if (MatchedInSavedSearchCount == 0) return "No Saved Search Matches";
      if (NotificationEffectivenessRate >= 90) return "Highly Effective";
      if (NotificationEffectivenessRate >= 70) return "Effective";
      if (NotificationEffectivenessRate >= 50) return "Moderately Effective";
      if (NotificationEffectivenessRate >= 25) return "Low Effectiveness";
      return "Very Low Effectiveness";
    }
  }
}

public class AdminSearchSystemOverviewDTO
{
  // Basic system metrics
  public int TotalCustomers { get; set; }
  public int CustomersWithSavedSearches { get; set; }
  public int TotalContacts { get; set; }
  public int CustomersWithNoSearches { get; set; }
  public int CustomersWithNoSavedSearches { get; set; }
  public int TotalSavedSearches { get; set; }
  public int ActiveSavedSearches { get; set; }
  public int TotalSearches { get; set; }
  public int OrphanedSearches { get; set; }

  // Advert metrics
  public int TotalActiveAdverts { get; set; }
  public int AdvertsWithNotifications { get; set; }
  public int AdvertsWithSavedSearchMatches { get; set; }
  public int AdvertsWithUnsavedSearchMatches { get; set; }

  // Simplified notification metrics
  public int TotalAdvertsNotifiedAbout { get; set; }
  public int TotalContactsNotified { get; set; }

  // Date context
  public DateTime ReportPeriodStart { get; set; }
  public DateTime ReportGenerated { get; set; }

  // Calculated engagement rates
  public decimal CustomerEngagementRate => TotalCustomers > 0 ? Math.Round((decimal)CustomersWithSavedSearches / TotalCustomers * 100, 1) : 0;
  public decimal ContactEngagementRate => TotalContacts > 0 ? Math.Round((decimal)CustomersWithNoSearches / TotalContacts * 100, 1) : 0;
  public decimal ActiveSearchRate => TotalSavedSearches > 0 ? Math.Round((decimal)ActiveSavedSearches / TotalSavedSearches * 100, 1) : 0;
  public decimal AdvertNotificationCoverage => TotalActiveAdverts > 0 ? Math.Round((decimal)AdvertsWithNotifications / TotalActiveAdverts * 100, 1) : 0;

  // Performance indicators
  public decimal AverageNotificationsPerAdvert => TotalActiveAdverts > 0 ? Math.Round((decimal)TotalAdvertsNotifiedAbout / TotalActiveAdverts, 1) : 0;
  public decimal AverageMatchesPerSavedSearch => TotalSavedSearches > 0 ? Math.Round((decimal)AdvertsWithSavedSearchMatches / TotalSavedSearches, 1) : 0;
  public decimal SystemNotificationEffectiveness => AdvertsWithSavedSearchMatches > 0 ? Math.Round((decimal)TotalAdvertsNotifiedAbout / AdvertsWithSavedSearchMatches * 100, 1) : 0;

  // Top-level insights
  public string SystemHealthStatus
  {
    get
    {
      if (ActiveSearchRate >= 80 && AdvertNotificationCoverage >= 60 && SystemNotificationEffectiveness >= 70)
        return "Excellent";
      if (ActiveSearchRate >= 60 && AdvertNotificationCoverage >= 40 && SystemNotificationEffectiveness >= 50)
        return "Good";
      if (ActiveSearchRate >= 40 && AdvertNotificationCoverage >= 25 && SystemNotificationEffectiveness >= 30)
        return "Fair";
      return "Needs Attention";
    }
  }

  // Quick stats for dashboard
  public List<SystemMetricSummaryDTO> KeyMetrics { get; set; } = new List<SystemMetricSummaryDTO>();
}

public class SystemMetricSummaryDTO
{
  public string Name { get; set; }
  public string Value { get; set; }
  public string Description { get; set; }
  public string TrendIndicator { get; set; } // Up, Down, Stable, Unknown
}

public class RecentAdvertDataDTO
{
  public Guid Id { get; set; }
  public string Vrm { get; set; }
}

public class AdminStatVendorDTO
{
  public Guid Id { get; set; }
  public string VendorName { get; set; }
}