﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Models.InventData;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers.AuctionImports;

[ApiController]
[Route("api/invent-user")]
[Authorize(Roles = "ADMIN, POWER_USER, IMPORT_ADMIN")]
public class InventUserController : ControllerBase
{
  private IInventUserService _inventUserService;

  public InventUserController(IInventUserService inventUserService)
  {
    _inventUserService = inventUserService;
  }

  [HttpGet("")]
  public async Task<IActionResult> Search([FromQuery] InventUserSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    var results = await _inventUserService.Search(searchDTO, cancellationToken);
    return Ok(results);
  }

  [HttpGet("{id}")]
  public async Task<IActionResult> Get(Guid id, CancellationToken cancellationToken)
  {
    var result = await _inventUserService.Get(id, cancellationToken);
    if (result == null)
    {
      return NotFound();
    }
    return Ok(result);
  }

  [HttpPost("create")]
  public async Task<IActionResult> Create([FromBody] InventUserCreateDTO dto, CancellationToken cancellationToken)
  {
    var result = await _inventUserService.Create(dto, cancellationToken);
    if (!result.IsValid)
    {
      return BadRequest("Create failed");
    }

    return CreatedAtAction(nameof(Get), new { id = result.DTO.Id }, result.DTO);
  }

  [HttpPatch("{id}")] 
  public async Task<IActionResult> Patch(Guid id, [FromBody] JsonPatchDocument<InventUser> patch, CancellationToken cancellationToken)
  {
    if (patch == null)
    {
      return BadRequest("Patch document cannot be null");
    }
    var result = await _inventUserService.Patch(id, patch, cancellationToken);
    if (!result.IsValid)
    {
      return BadRequest("Patch failed");
    }
    return Ok(result.DTO);
  }

  [HttpDelete("{id}")]
  public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken)
  {
    var success = await _inventUserService.Delete(id, cancellationToken);
    if (!success)
    {
      return NotFound();
    }
    return NoContent();
  }

  [HttpGet("all")]
  public async Task<IActionResult> GetAll(CancellationToken cancellationToken)
  {
    var results = await _inventUserService.GetAll(cancellationToken);
    return Ok(results);
  }
}
