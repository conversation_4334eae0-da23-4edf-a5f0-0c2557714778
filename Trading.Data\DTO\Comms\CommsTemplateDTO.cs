﻿using DotLiquid;
using System;
using System.Collections.Generic;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.Comms;

namespace Trading.API.Data.DTO.Comms
{
  public class CommsTemplateDTO : BaseModelEntityIntDTO
  {
    public Guid? CustomerId { get; set; }

    public CustomerDTO Customer { get; set; }

    public CommsTypeEnum? CommsType { get; set; }

    public uint? CommsEventId { get; set; }
    public CommsEventDTO CommsEvent { get; set; }

    public string Template { get; set; }
    public string SubjectTemplate { get; set; }
    public string HeaderTemplate { get; set; }
    public string BodyTemplate { get; set; }
    public string RecipientAddress { get; set; }
    public string BccAddress { get; set; }
    public string SenderAddress { get; set; }
    public string SenderName { get; set; }
    public string ReplyToAddress { get; set; }
    public string ReplyToName { get; set; }
    public string ReplyNumber { get; set; }
    public bool? Active { get; set; }
    public uint? WrapperId { get; set; }
    public CommsTemplateDTO Wrapper { get; set; }
    public bool? IsWrapper { get; set; }
    public string TemplateName { get; set; }
    public bool AllowDuplicate { get; set; }
    public string UniqueIdTemplate { get; set; }
    public string ImageUrlTemplate { get; set; }
  }
  public class CreateHashResponse
  {

    public Hash Hash;
    public Guid? CustomerId;
  }

  public class AddExcludedCustomerDTO
  {
    public uint? CommsTemplateId { get; set; }
    public Guid? CustomerId { get; set; }
  }

  public class CommsTemplateSearchDTO
  {
    public CommsTemplateFiltersDTO Filters { get; set; }  = new CommsTemplateFiltersDTO() {  };

  }

  public class CommsTemplateFiltersDTO
  {
    public uint? Id { get; set; }
    public Guid? CustomerId { get; set; }
    public bool? Active { get; set; }
    public string CommsEventCode { get; set; }
    public uint? CommsEventId { get; set; }
    public bool? IsWrapper { get; set; }
    public CommsTypeEnum? CommsType { get; set; }
    public CommsMergeModelEnum? MergeModel { get; set; }
  }

  public class CommsTemplateConstantsDTO
  {
    public string CompanyName { get; set; }
    public string PhoneNumber { get; set; }
    public string EmailAddress { get; set; }
  }

  public class CommsTemplateResponseDTO
  {
    public bool Success { get; set; }
    public string Subject { get; set; }
    public string Header { get; set; }
    public string Body { get; set; }
    public string UniqueId { get; set; }
    public string ModelId { get; set; }
    public string ImageUrl { get; set; }
    public List<string> RecipientAddress { get; set; } = new List<string>();
    public List<string> BccAddress { get; set; } = new List<string>();
    public CommsTemplateDTO CommsTemplate { get; set; }

    public List<string> Tags { get; set; } = new List<string>();
  }

  public class CommsPreviewTemplateResponseDTO : CommsTemplateResponseDTO
  {
    public Guid? CustomerId;
    public Hash Hash;
    public string UniqueId;
  }

  public class DeliverTemplateResponseDTO
  {
    public CommsHistory CommsHistory  { get; set; }
    public uint? CommsHistoryId { get; set; }
    public bool Success { get; set; }
  }

  public class CommsTemplatePreviewRequestDTO
  {
    public CommsTemplateDTO CommsTemplate { get; set; }
    public string PreviewId { get; set; }
    public string PreviewRecipient { get; set; }

    public ContactDTO User { get; set; }
  }
}