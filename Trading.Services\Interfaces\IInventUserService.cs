using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Models.InventData;

namespace Trading.Services.Interfaces
{
  public interface IInventUserService
  {
    /// <summary>
    /// Search for invent users with filtering and pagination
    /// </summary>
    Task<SearchResultDTO<InventUserDTO>> Search(InventUserSearchDTO searchDTO, CancellationToken cancellationToken);

    /// <summary>
    /// Get a specific invent user by ID
    /// </summary>
    Task<InventUserDTO> Get(Guid id, CancellationToken cancellationToken);

    /// <summary>
    /// Create a new invent user
    /// </summary>
    Task<ValidatedResultDTO<InventUserDTO>> Create(InventUserCreateDTO dto, CancellationToken cancellationToken);

    /// <summary>
    /// Update an existing invent user using JSON patch
    /// </summary>
    Task<ValidatedResultDTO<InventUserDTO>> Patch(Guid id, JsonPatchDocument<InventUser> patch, CancellationToken cancellationToken);

    /// <summary>
    /// Delete an invent user
    /// </summary>
    Task<bool> Delete(Guid id, CancellationToken cancellationToken);

    /// <summary>
    /// Get all invent users (for dropdowns, etc.)
    /// </summary>
    Task<IEnumerable<InventUserDTO>> GetAll(CancellationToken cancellationToken);
  }
}
