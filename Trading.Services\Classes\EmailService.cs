using brevo_csharp.Model;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;

namespace Trading.Services.Classes;

public class EmailService : IEmailService
{
  private readonly IFileService _fileService;
  private readonly EmailConfigDTO _sendGridDTO;

  public EmailService(IFileService fileService, IOptionsSnapshot<EmailConfigDTO> configuration)
  {
    _fileService = fileService;
    _sendGridDTO = configuration.Value;
  }

  public async Task<bool> SendEmail(SendEmailDTO dto, CancellationToken cancellationToken)
  {
    try
    {
      var client = new SendGridClient(_sendGridDTO.APIKey);
      var from = new EmailAddress(_sendGridDTO.Sender, _sendGridDTO.SenderName);

      var to = new EmailAddress(dto.ToEmail, dto.ToName);
      var msg = MailHelper.CreateSingleEmail(from, to, dto.Subject, dto.EmailText, dto.EmailBody);

      await client.SendEmailAsync(msg);
    }
    catch (Exception ex)
    {
      return false;
    }

    return true;
  }


  public async Task<bool> ValidateEmail(string emailAddress)
  {
    return true;
  }


  public async Task<bool> TestSendEmail(CancellationToken cancellationToken)
  {
    try
    {
      // read in basic email template 
      var template = await _fileService.ReadFileAsString(@"Email Templates\BasicLayout.html");

      template = template.Replace("[!$RenderEmailTitle$!]", "This is the email title");
      template = template.Replace("[!$RenderEmailBody$!]", "This is the body of the email");

      var client = new SendGridClient(_sendGridDTO.APIKey);
      var from = new EmailAddress("<EMAIL>", "Trade 2 Trade");
      var subject = "Test email from tading platform";
      //var to = new EmailAddress("<EMAIL>", "Dave");
      var to = new EmailAddress("<EMAIL>", "Pete");
      var plainTextContent = "Need plain text version of the template..";

      var htmlContent = template; // "<strong>and easy to do anywhere, even with C#</strong>";

      var msg = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);
      var response = await client.SendEmailAsync(msg);
    }
    catch (Exception ex)
    {
      return false;
    }
    return true;
  }

  public Task<GetEmailEventReport> GetTransactionalEmailEvents(DateTime? startDate = null, DateTime? endDate = null, long? limit = 2500, long? offset = 0, string email = null, long? templateId = null, string messageId = null, string eventType = null)
  {
    throw new NotImplementedException();
  }

  public Task<GetAggregatedReport> GetTransactionalEmailStats(DateTime? startDate = null, DateTime? endDate = null, long? days = null, string tag = null)
  {
    throw new NotImplementedException();
  }

  public Task<GetReports> GetDailyEmailStats(DateTime? startDate = null, DateTime? endDate = null, long? days = null, string tag = null)
  {
    throw new NotImplementedException();
  }

  public Task<GetEmailEventReport> GetEmailEventsForAddress(string emailAddress, DateTime? startDate = null, DateTime? endDate = null, long? limit = 100)
  {
    throw new NotImplementedException();
  }

  public Task<decimal?> GetTransactionalClickThroughRate(DateTime? startDate = null, DateTime? endDate = null, string tag = null)
  {
    throw new NotImplementedException();
  }

  public Task<TransactionalEmailMetricsDTO> GetTransactionalEmailMetrics(DateTime? startDate = null, DateTime? endDate = null, string tag = null)
  {
    throw new NotImplementedException();
  }

  public Task<GetEmailEventReport> GetEmailsForAdvert(string advertTag, DateTime? startDate = null, DateTime? endDate = null)
  {
    throw new NotImplementedException();
  }

  public Task<int> StoreRecentEmailEvents()
  {
    throw new NotImplementedException();
  }
}
