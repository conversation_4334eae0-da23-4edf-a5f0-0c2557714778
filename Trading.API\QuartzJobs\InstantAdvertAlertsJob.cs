﻿using Quartz;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.QuartzJobs
{
  public class InstantAdvertAlertsJob : IJob
  {
    private readonly IAdvertSearchService _advertSearchService;
    public InstantAdvertAlertsJob(IAdvertSearchService advertSearchService)
    {
      _advertSearchService = advertSearchService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
      await _advertSearchService.ProcessImmediateAlerts(new CancellationToken());
    }
  }
}
