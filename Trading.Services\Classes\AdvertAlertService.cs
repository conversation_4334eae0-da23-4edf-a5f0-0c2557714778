//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;
//using Microsoft.Extensions.Logging;
//using Trading.API.Data.DTO;
//using Trading.API.Data.DTO.Comms;
//using Trading.API.Data.DTO.Components.AdvertSearch;
//using Trading.API.Data.Enums;
//using Trading.API.Data.Models;
//using Trading.Services.ExternalDTO.Configs;
//using Trading.Services.Helpers;
//using Trading.Services.Interfaces;
//using AutoMapper;
//using Microsoft.Extensions.Options;
//using static Trading.Services.DTOMappingProfiles.MappingProfile;

//namespace Trading.Services.Classes;

//public class AdvertAlertService : IAdvertAlertService
//{
//  private readonly IAdvertSearchRepository _advertSearchRepository;
//  private readonly IMapper _mapper;
//  private readonly IEmailService _emailService;
//  private readonly ISMSService _smsService;
//  private readonly INotificationService _notificationService;
//  private readonly ICommsTemplateService _commsTemplateService;
//  private readonly ILogger<AdvertAlertService> _logger;
//  private readonly PortalConfigDTO _portalConfig;
//  private readonly IDataCacheService _dataCacheService;

//  // Configuration constants
//  private const int BATCH_SIZE = 100;
//  private const int MAX_CONCURRENT_SEARCHES = 5;
//  private const int MAX_ADVERTS_PER_EMAIL = 5;

//  public AdvertAlertService(
//      IAdvertSearchRepository advertSearchRepository,
//      IMapper mapper,
//      IEmailService emailService,
//      ISMSService smsService,
//      INotificationService notificationService,
//      ICommsTemplateService commsTemplateService,
//      ILogger<AdvertAlertService> logger,
//      IOptionsSnapshot<PortalConfigDTO> portalConfig,
//      IDataCacheService dataCacheService)
//  {
//    _advertSearchRepository = advertSearchRepository;
//    _mapper = mapper;
//    _emailService = emailService;
//    _smsService = smsService;
//    _notificationService = notificationService;
//    _commsTemplateService = commsTemplateService;
//    _logger = logger;
//    _portalConfig = portalConfig.Value;
//    _dataCacheService = dataCacheService;
//  }

//  public async Task ProcessDailyAlerts(CancellationToken cancellationToken)
//  {
//    var now = DateTime.Now;

//    // Get eligible adverts - avoid loading full entities
//    var eligibleAdverts = await _advertSearchRepository
//        .GetAdvertsByStatusAndNotificationProcessedAsync(StatusEnum.Active, null, null, cancellationToken);

//    if (!eligibleAdverts.Any())
//    {
//      _logger.LogDebug("No eligible adverts found for daily processing");
//      return;
//    }

//    var eligibleAdvertIds = eligibleAdverts.Select(x => x.Id).ToList();

//    // Map adverts once - reuse this throughout the method
//    var advertDTOMap = _mapper.Map<IEnumerable<AdvertAlertDTO>>(eligibleAdverts)
//        .ToDictionary(x => x.Id.Value);

//    // Get active searches
//    var searches = await _advertSearchRepository
//        .GetSavedSearchesByUpdateFrequencyAndStatusAsync(UpdateFrequencyEnum.Daily, StatusEnum.Active, cancellationToken);

//    // Filter valid searches upfront
//    var validSearches = searches
//        .Where(s => s.SendUpdates == true &&
//                   (s.SendSMS == true || s.SendEmail == true) &&
//                   s.Search?.SearchPhrase != null &&
//                   s.ContactId.HasValue)
//        .ToList();

//    if (!validSearches.Any())
//    {
//      _logger.LogDebug("No valid searches found for daily processing");
//      return;
//    }

//    // Process searches in batches to avoid memory pressure
//    var notifications = new List<(Guid advertId, Guid contactId)>();
//    var processedSearches = 0;

//    foreach (var searchBatch in validSearches.Chunk(BATCH_SIZE))
//    {
//      var batchNotifications = await ProcessSearchBatch(
//          searchBatch,
//          eligibleAdvertIds,
//          advertDTOMap,
//          cancellationToken);

//      notifications.AddRange(batchNotifications);
//      processedSearches += searchBatch.Length;

//      // Allow other operations to proceed
//      if (processedSearches % (BATCH_SIZE * 2) == 0)
//      {
//        await Task.Yield();
//      }
//    }

//    // Batch update all adverts at once
//    await UpdateAdvertNotificationStatus(eligibleAdvertIds, now, true, cancellationToken);
//  }

//  public async Task ProcessImmediateAlerts(CancellationToken cancellationToken)
//  {
//    var now = DateTime.Now;

//    // Get eligible adverts
//    var adverts = await _advertSearchRepository
//        .GetAdvertsByStatusAndNotificationProcessedAsync(StatusEnum.Active, null, null, cancellationToken);

//    if (!adverts.Any())
//    {
//      _logger.LogDebug("No eligible adverts found for immediate processing");
//      return;
//    }

//    var eligibleAdvertGuids = adverts.Select(x => x.Id).ToList();
//    var eligibleAdvertIds = adverts.Select(x => GuidHelper.Guid2Crc(x.Id)).ToList();

//    // Load full advert data
//    var advertList = await _advertSearchRepository.GetAdvertsByIdsAsync(eligibleAdvertGuids, cancellationToken);
//    var advertDTOMap = _mapper.Map<IEnumerable<AdvertAlertDTO>>(advertList)
//        .ToDictionary(x => x.Id.Value);

//    // Get active searches
//    var searches = await _advertSearchRepository
//        .GetSavedSearchesByUpdateFrequencyAndStatusAsync(UpdateFrequencyEnum.Immediate, StatusEnum.Active, cancellationToken);

//    var validSearches = searches
//        .Where(s => s.SendUpdates == true &&
//                   (s.SendSMS == true || s.SendEmail == true) &&
//                   s.Search?.SearchPhrase != null &&
//                   s.ContactId.HasValue)
//        .ToList();

//    if (!validSearches.Any())
//    {
//      _logger.LogDebug("No valid searches found for immediate processing");
//      await UpdateAdvertNotificationStatus(eligibleAdvertGuids, now, false, cancellationToken);
//      return;
//    }

//    // Load previously sent notifications
//    var previouslySent = await _notificationService
//        .GetSentNotificationsAsync(eligibleAdvertGuids, cancellationToken);

//    var notificationsSent = previouslySent
//        .GroupBy(n => n.advertId)
//        .ToDictionary(
//            g => g.Key,
//            g => new HashSet<Guid>(g.Select(n => n.contactId)));

//    var allNotifications = new List<(Guid advertId, Guid contactId)>();

//    foreach (var search in validSearches)
//    {
//      var phrase = BuildSearchPhrase(search.Search.SearchPhrase, eligibleAdvertIds);
//      var matchingAdverts = await _advertSearchRepository.GetSphAdvertsByPhraseAsync(phrase, cancellationToken);

//      var matchingAdvertIds = matchingAdverts
//          .Where(x => eligibleAdvertGuids.Contains(x.SphLink.Advert.Id))
//          .Select(x => x.SphLink.Advert.Id)
//          .ToList();

//      if (!matchingAdvertIds.Any())
//        continue;

//      foreach (var advertId in matchingAdvertIds)
//      {
//        if (!notificationsSent.TryGetValue(advertId, out var contactSet))
//        {
//          contactSet = new HashSet<Guid>();
//          notificationsSent[advertId] = contactSet;
//        }

//        if (!contactSet.Contains(search.ContactId.Value))
//        {
//          allNotifications.Add((advertId, search.ContactId.Value));
//          contactSet.Add(search.ContactId.Value);

//          // Send notifications
//          await SendNotifications(
//              search,
//              advertDTOMap,
//              new List<Guid> { advertId },
//              cancellationToken);
//        }
//      }

//      // Update search notification time
//      search.NotificationTime = now;
//    }

//    // Batch add all notifications
//    if (allNotifications.Any())
//    {
//      await _notificationService.AddNotificationsBatch(allNotifications);
//    }

//    // Update advert notification status
//    await UpdateAdvertNotificationStatus(eligibleAdvertGuids, now, false, cancellationToken);
//  }



//  public async Task<bool> ProcessEmailNotificationMatches(
//      List<Guid> matchingAdverts,
//      string email,
//      Guid alertId,
//      CancellationToken cancellationToken)
//  {
//    // Load adverts only for the matching IDs
//    var adverts = await _advertSearchRepository.GetAdvertsByIdsAsync(matchingAdverts, cancellationToken);
//    var advertDTOs = _mapper.Map<IEnumerable<AdvertAlertDTO>>(adverts).ToList();

//    return await ProcessEmailNotificationMatches(advertDTOs, matchingAdverts, email, alertId, cancellationToken);
//  }

//  public async Task<bool> ProcessEmailNotificationMatches(
//      List<AdvertAlertDTO> allAdverts,
//      List<Guid> matchingAdverts,
//      string email,
//      Guid alertId,
//      CancellationToken cancellationToken)
//  {
//    if (allAdverts == null || !matchingAdverts.Any())
//    {
//      return false;
//    }

//    // Use HashSet for O(1) lookups
//    var matchingSet = new HashSet<Guid>(matchingAdverts);

//    // Single pass to get unique adverts
//    var advertDTOs = allAdverts
//        .Where(x => x.Id.HasValue && matchingSet.Contains(x.Id.Value))
//        .GroupBy(a => new
//        {
//          a.Vehicle.Make.MakeName,
//          a.Vehicle.Model.ModelName,
//          a.Vehicle.Deriv.DerivName
//        })
//        .Select(g => g.First())
//        .Take(MAX_ADVERTS_PER_EMAIL)
//        .ToList();

//    if (!advertDTOs.Any())
//    {
//      return false;
//    }

//    // Prepare adverts for email
//    PrepareAdvertsForEmail(advertDTOs);

//    var firstAd = advertDTOs[0];
//    var hash = CreateEmailHash(email, firstAd, advertDTOs, alertId);

//    try
//    {
//      var eventCode = advertDTOs.Count > 1 ? "ALERT-MULTIPLE-ADVERT" : "ALERT-SINGLE-ADVERT";
//      await _commsTemplateService.ProcessEventWithHash(new ProcessEventWithHashDTO
//      {
//        EventCode = eventCode,
//        Hash = hash,
//        UniqueId = $"{email}-{firstAd.Id}-{DateTime.UtcNow:yyyyMMddHH}"
//      }, cancellationToken);

//      return true;
//    }
//    catch (Exception ex)
//    {
//      _logger.LogError(ex, "Error processing email notification for alert {AlertId}", alertId);
//      return false;
//    }
//  }

//  #region Private Helper Methods

//  private async Task<List<(Guid advertId, Guid contactId)>> ProcessSearchBatch(
//      SavedSearch[] searches,
//      List<Guid> eligibleAdvertIds,
//      Dictionary<Guid, AdvertAlertDTO> advertDTOMap,
//      CancellationToken cancellationToken)
//  {
//    var notifications = new List<(Guid advertId, Guid contactId)>();

//    foreach (var search in searches)
//    {
//      try
//      {
//        var phrase = BuildSearchPhrase(search.Search.SearchPhrase, eligibleAdvertIds);
//        var matchingAdverts = await _advertSearchRepository.GetSphAdvertsByPhraseAsync(phrase, cancellationToken);

//        var matchingAdvertIds = matchingAdverts
//            .Where(x => eligibleAdvertIds.Contains(x.SphLink.Advert.Id))
//            .Select(x => x.SphLink.Advert.Id)
//            .ToList();

//        if (matchingAdvertIds.Any())
//        {
//          // Add notifications
//          notifications.AddRange(matchingAdvertIds.Select(id => (id, search.ContactId.Value)));

//          // Send notifications
//          await SendNotifications(search, advertDTOMap, matchingAdvertIds, cancellationToken);
//        }
//      }
//      catch (Exception ex)
//      {
//        _logger.LogError(ex, "Error processing search {SearchId}", search.Id);
//      }
//    }

//    return notifications;
//  }

//  private string BuildSearchPhrase(string originalPhrase, List<Guid> advertIds)
//  {
//    var crcIds = advertIds.Select(id => GuidHelper.Guid2Crc(id));
//    return BuildSearchPhrase(originalPhrase, crcIds);
//  }

//  private string BuildSearchPhrase(string originalPhrase, IEnumerable<uint> advertIds)
//  {
//    var filterToInsert = $"advertIdCRC,{string.Join(',', advertIds)}";

//    var filterIndex = originalPhrase.IndexOf(";filter=", StringComparison.Ordinal);
//    if (filterIndex >= 0)
//    {
//      // Insert at the beginning of existing filters
//      return originalPhrase.Insert(filterIndex + 8, filterToInsert + ",");
//    }

//    // Add new filter section after first semicolon
//    var firstSemicolon = originalPhrase.IndexOf(';');
//    if (firstSemicolon >= 0)
//    {
//      return originalPhrase.Insert(firstSemicolon, $";filter={filterToInsert}");
//    }

//    // No semicolon found, append to end
//    return $"{originalPhrase};filter={filterToInsert}";
//  }

//  private async Task SendNotifications(
//      SavedSearch search,
//      Dictionary<Guid, AdvertAlertDTO> advertDTOMap,
//      List<Guid> matchingAdvertIds,
//      CancellationToken cancellationToken)
//  {
//    if (search.SendEmail == true && !string.IsNullOrEmpty(search.Contact?.Email))
//    {
//      var advertsToSend = matchingAdvertIds
//          .Where(id => advertDTOMap.ContainsKey(id))
//          .Select(id => advertDTOMap[id])
//          .ToList();

//      await ProcessEmailNotificationMatches(
//          advertsToSend,
//          matchingAdvertIds,
//          search.Contact.Email,
//          search.Id,
//          cancellationToken);
//    }

//    if (search.SendSMS == true)
//    {
//      await SendSmsNotification(search.Contact);
//    }
//  }

//  private async Task SendSmsNotification(Contact contact)
//  {
//    if (contact == null) return;

//    var phoneNumber = _smsService.IsMobileNumber(contact.Phone1) ? contact.Phone1 :
//                     _smsService.IsMobileNumber(contact.Phone2) ? contact.Phone2 : null;

//    if (!string.IsNullOrEmpty(phoneNumber))
//    {
//      try
//      {
//        await _smsService.SendSMS(
//            phoneNumber,
//            "There are new vehicles matching your requirements. Login to view them");
//      }
//      catch (Exception ex)
//      {
//        _logger.LogError(ex, "Failed to send SMS to {PhoneNumber}", phoneNumber.Substring(0, 3) + "****");
//      }
//    }
//  }

//  private void PrepareAdvertsForEmail(List<AdvertAlertDTO> advertDTOs)
//  {
//    foreach (var advert in advertDTOs)
//    {
//      advert.AdvertURL = URLHelper.AdvertURL(advert.Id.Value, _portalConfig.URL);
//      advert.Vehicle.PrimaryImageURL += "?tr=h-500";

//      // Only take first 3 images for email
//      advert.MediaURL = advert.Vehicle.VehicleMedia
//          .Where(a => a.Id != advert.Vehicle.PrimaryImageId)
//          .Take(3)
//          .Select(z => URLHelper.VehicleMediaUrl(new VehicleMediaURLDTO
//          {
//            CustomerId = advert.CustomerId.Value,
//            VehicleId = advert.VehicleId.Value,
//            VehicleMediaId = z.Id.Value,
//            Height = 200
//          }))
//          .ToList();
//    }
//  }

//  private DotLiquid.Hash CreateEmailHash(
//      string email,
//      AdvertAlertDTO firstAd,
//      List<AdvertAlertDTO> advertDTOs,
//      Guid alertId)
//  {
//    var hash = new DotLiquid.Hash
//    {
//      ["recipient_email"] = email,
//      ["listing"] = firstAd,
//      ["image-1"] = firstAd.MediaURL.ElementAtOrDefault(0) ?? "",
//      ["image-2"] = firstAd.MediaURL.ElementAtOrDefault(1) ?? "",
//      ["image-3"] = firstAd.MediaURL.ElementAtOrDefault(2) ?? "",
//      ["unsubscribe-url"] = URLHelper.AlertUnsubscribeURL(alertId, _portalConfig.URL),
//      ["listings"] = advertDTOs
//    };

//    if (advertDTOs.Count > 1)
//    {
//      hash["make_list"] = string.Join(" & ", advertDTOs.Select(x => x.Vehicle.Make.MakeName).Distinct());
//    }

//    return hash;
//  }

//  private async Task UpdateAdvertNotificationStatus(
//      List<Guid> advertIds,
//      DateTime processedTime,
//      bool isDaily,
//      CancellationToken cancellationToken)
//  {
//    // This would be more efficient as a bulk update in the repository
//    var adverts = _advertSearchRepository.GetAdvertsByIds(advertIds);

//    await adverts.ForEachAsync(BATCH_SIZE, async advert =>
//    {
//      if (isDaily)
//      {
//        advert.DailyNotificationProcessed = processedTime;
//      }
//      else
//      {
//        advert.ImmediateNotificationProcessed = processedTime;
//      }
//    });

//    await _advertSearchRepository.SaveChangesAsync(cancellationToken);
//  }

//  #endregion
//}

//// Extension method for parallel foreach with batching
//public static class AsyncExtensions
//{
//  public static async Task ForEachAsync<T>(
//      this IQueryable<T> source,
//      int batchSize,
//      Func<T, Task> action)
//  {
//    var items = source.ToList();
//    var semaphore = new SemaphoreSlim(batchSize);
//    var tasks = items.Select(async item =>
//    {
//      await semaphore.WaitAsync();
//      try
//      {
//        await action(item);
//      }
//      finally
//      {
//        semaphore.Release();
//      }
//    });

//    await Task.WhenAll(tasks);
//  }
//}