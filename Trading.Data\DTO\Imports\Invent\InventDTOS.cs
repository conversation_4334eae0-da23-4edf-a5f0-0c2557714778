﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Imports.Invent;

public class InventTyreItem
{
  public string Position { get; set; } = "";
  public string Make { get; set; } = "";
  public string Condition { get; set; } = "";
}

public class InventInspectionResult
{
  public DateTime InspectionDate { get; set; }
  public List<InventDamageItem> DamageItems { get; set; }
  public List<InventTyreItem> TyreItems { get; set; } = new List<InventTyreItem>();
  public string InteriorNotes { get; set; } = "";
}

public class InventDamageItem
{
  public string Component { get; set; }
  public string Condition { get; set; }
  public string Severity { get; set; }
  public string ImageUrl { get; set; }
  public string ImageFileName { get; set; }
}

public class InventServiceHistoryResult
{
  public List<InventServiceHistoryItem> ServiceHistoryItems { get; set; } = new List<InventServiceHistoryItem>();
  public string ServiceHistoryStatus { get; set; }
  public string ServiceHistoryDocumentation { get; set; }
  public int? ServiceBookNumStamps { get; set; }
  public DateTime? LastServiceDate { get; set; }
  public uint? LastServiceMiles { get; set; }
  public DateTime? NextServiceDate { get; set; }
  public uint? ServiceIntervalMiles { get; set; }
  public uint? ServiceIntervalMonths { get; set; }
}

public class InventServiceHistoryItem
{
  public DateTime ServiceDate { get; set; }
  public uint Odometer { get; set; }
  public string DealerName { get; set; }
  public string DealerType { get; set; } 
  public string Documentation { get; set; }
}


/// <summary>
/// DTO for auctions in search results
/// </summary>
public class InventAuctionDTO
{
  public Guid Id { get; set; }
  public string Title { get; set; }
  public DateTime? DateTime { get; set; }
  public DateTime? EndDateTime { get; set; }
  public int ActivityStatus { get; set; }
  public string ActivityStatusText => GetActivityStatusText(ActivityStatus);
  public string AuctionTypeTitle { get; set; }
  public int AuctionLocationId { get; set; }
  public string AuctionLocationTitle { get; set; }
  public int LotsCount { get; set; }

  private string GetActivityStatusText(int status)
  {
    return status switch
    {
      1 => "Upcoming",
      2 => "In-Progress",
      3 => "Paused",
      4 => "Finished",
      5 => "Closed",
      _ => "Unknown"
    };
  }
}

/// <summary>
/// DTO for auction lots in search results
/// </summary>
public class InventAuctionLotDTO
{
  public Guid Id { get; set; }
  public Guid AuctionId { get; set; }

  public long? InventVehicleId { get; set; } 

  public Guid? VehicleId { get; set; } // for vehicles on the platform to allow withdrawal etc.
  public string Vrm { get; set; }
  public string Vin { get; set; }
  public string Manufacturer { get; set; }
  public string Model { get; set; }
  public string Variant { get; set; }
  public int? Year { get; set; }
  public int? Mileage { get; set; }

  public bool ServiceHistory { get; set; }
  public string BodyType { get; set; }
  public string Colour { get; set; }
  public string FuelType { get; set; }
  public string Transmission { get; set; }
  public decimal? ReservePrice { get; set; }
  public decimal? CurrentBid { get; set; }
  public int Status { get; set; }
  public string StatusText => GetStatusText(Status);
  public string AuctionTitle { get; set; }
  public DateTime? AuctionDateTime { get; set; }
  public string DefaultImageUrl { get; set; }
  public DateTime? ProcessedDate { get; set; }
  public DateTime? SoldDate { get; set; }
  public bool RecentlySold { get; set; }
  public DateTime? EndDateTime { get; set; }
  public Guid? AdvertId { get; set; }

  public DateTime? Added { get; set; }
  public DateTime? Updated { get; set; }

  public List<string> ImageUrls { get; set; }

  public string ImportTextError { get; set; }

  private string GetStatusText(int status)
  {
    return status switch
    {
      1 => "Available",
      2 => "Imported",
      3 => "Provisional",
      4 => "Actioned",
      5 => "Withdrawn",
      6 => "Deleted",
      _ => "Unknown"
    };
  }
}
