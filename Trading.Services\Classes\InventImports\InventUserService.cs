using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Enums;
using Trading.API.Data.Models.InventData;
using Trading.Services.Extensions;
using Trading.Services.Interfaces;

namespace Trading.Services.Classes.InventImports
{
  public class InventUserService : IInventUserService
  {
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;
    private readonly ILogger<InventUserService> _logger;
    private readonly IUserService _userService;

    public InventUserService(
        TradingContext tradingContext,
        IMapper mapper,
        ILogger<InventUserService> logger,
        IUserService userService)
    {
      _tradingContext = tradingContext ?? throw new ArgumentNullException(nameof(tradingContext));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _userService = userService ?? throw new ArgumentNullException(nameof(userService));
    }

    public async Task<SearchResultDTO<InventUserDTO>> Search(InventUserSearchDTO searchDTO, CancellationToken cancellationToken)
    {
      var query = _tradingContext.InventUsers
          .AsNoTracking()
          .Include(u => u.Contact)
          .Include(u => u.Customer)
          .Include(u => u.Address)
          .AsQueryable();

      // Apply filters
      if (searchDTO.Filters != null)
      {
        if (searchDTO.Filters.Id.HasValue)
        {
          query = query.Where(u => u.Id == searchDTO.Filters.Id.Value);
        }

        if (!string.IsNullOrEmpty(searchDTO.Filters.CompanyName))
        {
          query = query.Where(u => u.CompanyName.Contains(searchDTO.Filters.CompanyName));
        }

        if (!string.IsNullOrEmpty(searchDTO.Filters.AuctionId))
        {
          query = query.Where(u => u.AuctionId.Contains(searchDTO.Filters.AuctionId));
        }

        if (!string.IsNullOrEmpty(searchDTO.Filters.ContactName))
        {
          query = query.Where(u => u.Contact.ContactName.Contains(searchDTO.Filters.ContactName));
        }

        if (!string.IsNullOrEmpty(searchDTO.Filters.CustomerName))
        {
          query = query.Where(u => u.Customer.CustomerName.Contains(searchDTO.Filters.CustomerName));
        }

        if (!string.IsNullOrEmpty(searchDTO.Filters.CustomerRef))
        {
          query = query.Where(u => u.CustomerRef.Contains(searchDTO.Filters.CustomerRef));
        }

        if (searchDTO.Filters.ContactId.HasValue)
        {
          query = query.Where(u => u.ContactId == searchDTO.Filters.ContactId.Value);
        }

        if (searchDTO.Filters.CustomerId.HasValue)
        {
          query = query.Where(u => u.CustomerId == searchDTO.Filters.CustomerId.Value);
        }

        if (searchDTO.Filters.AddressId.HasValue)
        {
          query = query.Where(u => u.AddressId == searchDTO.Filters.AddressId.Value);
        }

        if (searchDTO.Filters.LastImportedDateFrom.HasValue)
        {
          query = query.Where(u => u.LastImportedDate >= searchDTO.Filters.LastImportedDateFrom.Value);
        }

        if (searchDTO.Filters.LastImportedDateTo.HasValue)
        {
          query = query.Where(u => u.LastImportedDate <= searchDTO.Filters.LastImportedDateTo.Value);
        }

        // Apply status filter from base filter
        if (searchDTO.Filters.StatusId.HasValue)
        {
          query = query.Where(u => u.StatusId == searchDTO.Filters.StatusId.Value);
        }
        else
        {
          // Default to active records only
          query = query.Where(u => u.StatusId != (uint)StatusEnum.Deleted);
        }
      }

      // Apply security - non-admin users can only see their own records
      if (!_userService.IsAdminOrGreater())
      {
        var currentContactId = _userService.GetContactId();
        if (currentContactId.HasValue)
        {
          query = query.Where(u => u.ContactId == currentContactId.Value);
        }
        else
        {
          // If no contact ID, return empty results
          return new SearchResultDTO<InventUserDTO>
          {
            Results = new List<InventUserDTO>(),
            TotalItems = 0
          };
        }
      }

      // Get total count before pagination
      var totalItems = await query.CountAsync(cancellationToken);

      // Apply ordering
      if (searchDTO.Order?.Any() == true)
      {
        foreach (var order in searchDTO.Order)
        {
          switch (order.Column?.ToLower())
          {
            case "companyname":
              query = order.Descending == true ? query.OrderByDescending(u => u.CompanyName) : query.OrderBy(u => u.CompanyName);
              break;
            case "auctionid":
              query = order.Descending == true ? query.OrderByDescending(u => u.AuctionId) : query.OrderBy(u => u.AuctionId);
              break;
            case "lastimporteddate":
              query = order.Descending == true ? query.OrderByDescending(u => u.LastImportedDate) : query.OrderBy(u => u.LastImportedDate);
              break;
            case "contactname":
              query = order.Descending == true ? query.OrderByDescending(u => u.Contact.ContactName) : query.OrderBy(u => u.Contact.ContactName);
              break;
            case "customername":
              query = order.Descending == true ? query.OrderByDescending(u => u.Customer.CustomerName) : query.OrderBy(u => u.Customer.CustomerName);
              break;
            default:
              query = order.Descending == true ? query.OrderByDescending(u => u.Updated) : query.OrderBy(u => u.Updated);
              break;
          }
        }
      }
      else
      {
        // Default ordering
        query = query.OrderByDescending(u => u.Updated);
      }

      // Apply pagination
      if (searchDTO.Offset.HasValue)
      {
        query = query.Skip(searchDTO.Offset.Value);
      }

      if (searchDTO.Limit.HasValue)
      {
        query = query.Take(searchDTO.Limit.Value);
      }

      var entities = await query.ToListAsync(cancellationToken);

      var results = _mapper.Map<IEnumerable<InventUserDTO>>(entities);

      return new SearchResultDTO<InventUserDTO>
      {
        Results = results,
        TotalItems = totalItems
      };
    }

    public async Task<InventUserDTO> Get(Guid id, CancellationToken cancellationToken)
    {
      var entity = await _tradingContext.InventUsers
          .AsNoTracking()
          .Include(u => u.Contact)
          .Include(u => u.Customer)
          .Include(u => u.Address)
          .FirstOrDefaultAsync(u => u.Id == id && u.StatusId != (uint)StatusEnum.Deleted, cancellationToken);

      if (entity == null)
      {
        return null;
      }

      // Apply security - non-admin users can only see their own records
      if (!_userService.IsAdminOrGreater())
      {
        var currentContactId = _userService.GetContactId();
        if (!currentContactId.HasValue || entity.ContactId != currentContactId.Value)
        {
          return null;
        }
      }

      return _mapper.Map<InventUserDTO>(entity);
    }

    public async Task<ValidatedResultDTO<InventUserDTO>> Create(InventUserCreateDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        // Validate required fields
        if (dto.ContactId == Guid.Empty)
        {
          return new ValidatedResultDTO<InventUserDTO>
          {
            IsValid = false,
            Message = "ContactId is required",
            HTTPStatus = HTTPStatusEnum.BadRequest
          };
        }

        if (dto.CustomerId == Guid.Empty)
        {
          return new ValidatedResultDTO<InventUserDTO>
          {
            IsValid = false,
            Message = "CustomerId is required",
            HTTPStatus = HTTPStatusEnum.BadRequest
          };
        }

        if (dto.AddressId == Guid.Empty)
        {
          return new ValidatedResultDTO<InventUserDTO>
          {
            IsValid = false,
            Message = "AddressId is required",
            HTTPStatus = HTTPStatusEnum.BadRequest
          };
        }

        // Check if AuctionId is unique
        if (!string.IsNullOrEmpty(dto.AuctionId))
        {
          var existingUser = await _tradingContext.InventUsers
              .AsNoTracking()
              .FirstOrDefaultAsync(u => u.AuctionId == dto.AuctionId && u.StatusId != (uint)StatusEnum.Deleted, cancellationToken);

          if (existingUser != null)
          {
            return new ValidatedResultDTO<InventUserDTO>
            {
              IsValid = false,
              Message = "AuctionId already exists",
              HTTPStatus = HTTPStatusEnum.BadRequest
            };
          }
        }

        // Apply security - non-admin users can only create records for themselves
        if (!_userService.IsAdminOrGreater())
        {
          var currentContactId = _userService.GetContactId();
          if (!currentContactId.HasValue || dto.ContactId != currentContactId.Value)
          {
            return new ValidatedResultDTO<InventUserDTO>
            {
              IsValid = false,
              Message = "Unauthorized to create record for this contact",
              HTTPStatus = HTTPStatusEnum.Forbidden
            };
          }
        }

        var entity = new InventUser
        {
          Id = Guid.NewGuid(),
          CompanyName = dto.CompanyName,
          AuctionId = dto.AuctionId,
          ContactId = dto.ContactId,
          CustomerId = dto.CustomerId,
          AddressId = dto.AddressId,
          LogoSwapPixelHeight = dto.LogoSwapPixelHeight,
          CustomerRef = dto.CustomerRef,
          AuctionMarkup = (uint)dto.AuctionMarkup,
          Added = DateTime.UtcNow,
          Updated = DateTime.UtcNow,
          StatusId = (uint)StatusEnum.Active
        };

        _tradingContext.InventUsers.Add(entity);
        await _tradingContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation($"Created InventUser with ID {entity.Id}");

        // Load the created entity with related data
        var createdEntity = await _tradingContext.InventUsers
            .AsNoTracking()
            .Include(u => u.Contact)
            .Include(u => u.Customer)
            .Include(u => u.Address)
            .FirstOrDefaultAsync(u => u.Id == entity.Id, cancellationToken);

        var result = _mapper.Map<InventUserDTO>(createdEntity);

        return new ValidatedResultDTO<InventUserDTO>
        {
          IsValid = true,
          DTO = result,
          HTTPStatus = HTTPStatusEnum.Created
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error creating InventUser: {ex.Message}");
        return new ValidatedResultDTO<InventUserDTO>
        {
          IsValid = false,
          Message = "An error occurred while creating the record",
          HTTPStatus = HTTPStatusEnum.InternalServerError
        };
      }
    }

    public async Task<ValidatedResultDTO<InventUserDTO>> Patch(Guid id, JsonPatchDocument<InventUser> patch, CancellationToken cancellationToken)
    {
      var item = await _tradingContext.InventUsers
        .Where(x => x.Id == id).FirstOrDefaultAsync();

      patch.FilterPatch();
      patch.ApplyTo(item);
      item.Updated = DateTime.Now;
      item.StatusId = (uint)StatusEnum.Active;

      await _tradingContext.SaveChangesAsync();

      var returnVal = _mapper.Map<InventUser, InventUserDTO>(item);
      return new ValidatedResultDTO<InventUserDTO> { DTO = returnVal, IsValid = true };
    }

    public async Task<bool> Delete(Guid id, CancellationToken cancellationToken)
    {
      var inventUser = await _tradingContext.InventUsers
          .FirstOrDefaultAsync(u => u.Id == id && u.StatusId != (uint)StatusEnum.Deleted, cancellationToken);

      if (inventUser == null)
      {
        return false;
      }

      inventUser.StatusId = (uint)StatusEnum.Deleted;
      inventUser.Updated = DateTime.Now;
      await _tradingContext.SaveChangesAsync(cancellationToken);

      return true;
    }

    public async Task<IEnumerable<InventUserDTO>> GetAll(CancellationToken cancellationToken)
    {
      if (!_userService.IsAdminOrGreater())
      {
        return Enumerable.Empty<InventUserDTO>();
      }

      var users = await _tradingContext.InventUsers
          .AsNoTracking()
          .Include(u => u.Contact)
          .Include(u => u.Customer)
          .Where(u => u.StatusId != (uint)StatusEnum.Deleted)
          .OrderBy(u => u.CompanyName)
          .ToListAsync(cancellationToken);

      return _mapper.Map<IEnumerable<InventUserDTO>>(users);
    }
  }
}
