﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;

namespace Trading.Services.InspectCollect.Interfaces;
public interface ICAutoTraderSettingsInterface
{
  ICAutoTraderSettingsDTO GetSettingsForContainerGroup(Guid icContainerGroupId, CancellationToken cancellationToken = default);
  ICAutoTraderSettingsDTO GetDefaultSettings(CancellationToken cancellationToken = default);
}
