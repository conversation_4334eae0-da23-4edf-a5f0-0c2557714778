﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Admin;

namespace Trading.Services.Interfaces;
public interface IAdminSearchStatsService
{
  /// <summary>
  /// Gets customer-centric search analytics with saved search details and notification statistics.
  /// Groups results by customer and provides comprehensive search effectiveness metrics.
  /// </summary>
  /// <param name="request">Filter and pagination parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Paginated list of customer search analytics</returns>
  Task<SearchResultDTO<AdminCustomerSearchAnalyticsDTO>> GetCustomerSearchAnalytics(
      AdminCustomerSearchAnalyticsRequestDTO request,
      CancellationToken cancellationToken);

  /// <summary>
  /// Gets advert-centric notification analytics with search matching and notification statistics.
  /// Provides detailed metrics for how adverts perform across different search types.
  /// </summary>
  /// <param name="request">Filter and pagination parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Paginated list of advert notification analytics</returns>
  Task<SearchResultDTO<AdvertNotificationStatsDTO>> GetAdvertNotificationAnalytics(
      AdminAdvertAnalyticsRequestDTO request,
      CancellationToken cancellationToken);

  /// <summary>
  /// Gets high-level system overview metrics for search and notification effectiveness.
  /// Provides insights into overall system health and performance indicators.
  /// </summary>
  /// <param name="fromDate">Start date for metrics calculation (defaults to 30 days ago)</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>System overview with key performance metrics</returns>
  Task<AdminSearchSystemOverviewDTO> GetSearchSystemOverview(
      DateTime? fromDate,
      CancellationToken cancellationToken);

  Task<List<AdminStatVendorDTO>> GetAdminStatVendors(CancellationToken cancellationToken);
}
