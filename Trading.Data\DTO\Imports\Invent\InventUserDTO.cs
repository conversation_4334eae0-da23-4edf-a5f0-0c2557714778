﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Imports.Invent;
public class InventUserDTO : BaseModelEntityDTO
{
  public string CompanyName { get; set; }

  public string AuctionId { get; set; }

  public DateTime? LastImportedDate { get; set; }


  public Guid ContactId { get; set; }
  public string ContactName { get; set; }

  public Guid CustomerId { get; set; }
  public string CustomerName { get; set; }

  public Guid AddressId { get; set; }

  // if > 0 then logo swap is enabled and the value is the height in pixels to cut and paste the logo
  public int LogoSwapPixelHeight { get; set; }

  public string CustomerRef { get; set; } // used for imageKit prefix and appraisal reference

  public uint AuctionMarkup { get; set; } // unit value to add to the imported lot (i.e. GBP, EUR, etc.)
}

public class InventUserSearchDTO : BaseSearchDTO
{
  public InventUserSearchFilters Filters { get; set; } = new InventUserSearchFilters();
}

public class InventUserSearchFilters : BaseFilterGuid
{
  public string CompanyName { get; set; }
  public string AuctionId { get; set; }
  public string ContactName { get; set; }
  public string CustomerName { get; set; }
  public string CustomerRef { get; set; }
  public DateTime? LastImportedDateFrom { get; set; }
  public DateTime? LastImportedDateTo { get; set; }
  public Guid? ContactId { get; set; }
  public Guid? CustomerId { get; set; }
  public Guid? AddressId { get; set; }
}

public class InventUserCreateDTO
{
  public string CompanyName { get; set; }
  public string AuctionId { get; set; }
  public Guid ContactId { get; set; }
  public Guid CustomerId { get; set; }
  public Guid AddressId { get; set; }
  public int LogoSwapPixelHeight { get; set; }
  public string CustomerRef { get; set; }

  public int AuctionMarkup { get; set; }
  }
