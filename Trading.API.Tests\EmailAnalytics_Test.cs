using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.Models;
using Trading.API.Tests.Common;
using Trading.Services.Classes;
using Trading.Services.Interfaces;
using Xunit;
using System;
using System.Threading.Tasks;
using System.Linq;

namespace Trading.API.Tests
{
  public class EmailAnalytics_Test : TestBase
  {
    public EmailAnalytics_Test()
    {
    }

    [Fact]
    public async Task AdvertEvent_CanBeCreatedAndRetrieved()
    {
      // Arrange
      var advert = new Advert
      {
        Id = Guid.NewGuid(),
        Headline = "Test Advert",
        Description = "Test Description",
        ContactId = Guid.NewGuid(),
        CustomerId = Guid.NewGuid()
      };

      await _context.Adverts.AddAsync(advert);
      await _context.SaveChangesAsync();

      var advertEvent = new AdvertEvent
      {
        Event = "opened",
        Email = "<EMAIL>",
        Date = DateTime.UtcNow,
        Subject = "Test Email Subject",
        MessageId = "test-message-id-123",
        Tag = $"alert-advert-{advert.Id}",
        Ip = "***********",
        From = "<EMAIL>",
        AdvertId = advert.Id
      };

      // Act
      await _context.AdvertEvents.AddAsync(advertEvent);
      await _context.SaveChangesAsync();

      // Assert
      var retrievedEvent = await _context.AdvertEvents
          .Include(ae => ae.Advert)
          .FirstOrDefaultAsync(ae => ae.MessageId == "test-message-id-123");

      Assert.NotNull(retrievedEvent);
      Assert.Equal("opened", retrievedEvent.Event);
      Assert.Equal("<EMAIL>", retrievedEvent.Email);
      Assert.Equal("Test Email Subject", retrievedEvent.Subject);
      Assert.Equal(advert.Id, retrievedEvent.AdvertId);
      Assert.NotNull(retrievedEvent.Advert);
      Assert.Equal("Test Advert", retrievedEvent.Advert.Headline);
    }

    [Fact]
    public async Task AdvertEvent_DuplicateCheck_WorksCorrectly()
    {
      // Arrange
      var advertEvent1 = new AdvertEvent
      {
        Event = "opened",
        Email = "<EMAIL>",
        Date = DateTime.UtcNow,
        Subject = "Test Email Subject",
        MessageId = "duplicate-test-123",
        Tag = "alert-advert-12345",
        Ip = "***********",
        From = "<EMAIL>"
      };

      var advertEvent2 = new AdvertEvent
      {
        Event = "opened",
        Email = "<EMAIL>",
        Date = DateTime.UtcNow.AddMinutes(5),
        Subject = "Test Email Subject",
        MessageId = "duplicate-test-123", // Same MessageId
        Tag = "alert-advert-12345",
        Ip = "***********",
        From = "<EMAIL>"
      };

      // Act
      await _context.AdvertEvents.AddAsync(advertEvent1);
      await _context.SaveChangesAsync();

      // Check if duplicate exists
      var existingEvent = await _context.AdvertEvents
          .FirstOrDefaultAsync(ae => 
              ae.MessageId == advertEvent2.MessageId &&
              ae.Event == advertEvent2.Event &&
              ae.Email == advertEvent2.Email);

      // Assert
      Assert.NotNull(existingEvent);
      Assert.Equal(advertEvent1.Id, existingEvent.Id);
    }

    [Fact]
    public void ExtractAdvertIdFromTag_WorksCorrectly()
    {
      // Test the regex pattern that would be used in the service
      var tag1 = "alert-advert-12345678-1234-1234-1234-123456789012";
      var tag2 = "alert-advert-invalid-guid";
      var tag3 = "other-tag-format";

      // This simulates the regex logic from the service
      var guidPattern = @"alert-advert-([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})";
      
      var match1 = System.Text.RegularExpressions.Regex.Match(tag1, guidPattern);
      var match2 = System.Text.RegularExpressions.Regex.Match(tag2, guidPattern);
      var match3 = System.Text.RegularExpressions.Regex.Match(tag3, guidPattern);

      Assert.True(match1.Success);
      Assert.Equal("12345678-1234-1234-1234-123456789012", match1.Groups[1].Value);
      
      Assert.False(match2.Success);
      Assert.False(match3.Success);
    }
  }
}
