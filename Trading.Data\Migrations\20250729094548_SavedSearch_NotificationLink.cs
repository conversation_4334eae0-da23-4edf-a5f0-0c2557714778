﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
  /// <inheritdoc />
  public partial class SavedSearch_NotificationLink : Migration
  {
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
      // Step 1: Add nullable column first
      migrationBuilder.AddColumn<Guid?>(
          name: "SavedSearchId",
          table: "Notification",
          type: "binary(16)",
          nullable: true);

      // Step 2: Create index on the nullable column
      migrationBuilder.CreateIndex(
          name: "IX_Notification_SavedSearchId",
          table: "Notification",
          column: "SavedSearchId");

      // Step 3: Retrospectively link notifications to saved searches
      // This links each notification to the most recent active saved search for that contact
      // Note: This is a best-effort approach since we can't run Sphinx queries in SQL
      migrationBuilder.Sql(@"
                UPDATE Notification n
                INNER JOIN (
                    SELECT 
                        n.Id as NotificationId,
                        (
                            SELECT ss.Id 
                            FROM SavedSearch ss
                            WHERE ss.ContactId = n.ContactId 
                            AND ss.StatusId = 1 -- Active status
                            AND ss.Added <= n.Added -- Search existed when notification was sent
                            ORDER BY ss.Added DESC -- Most recent search wins
                            LIMIT 1
                        ) as SavedSearchId
                    FROM Notification n
                    WHERE n.SavedSearchId IS NULL
                ) mapping ON n.Id = mapping.NotificationId
                SET n.SavedSearchId = mapping.SavedSearchId
                WHERE mapping.SavedSearchId IS NOT NULL;
            ");

      // Step 4: Add foreign key constraint
      migrationBuilder.AddForeignKey(
          name: "FK_Notification_SavedSearch_SavedSearchId",
          table: "Notification",
          column: "SavedSearchId",
          principalTable: "SavedSearch",
          principalColumn: "Id",
          onDelete: ReferentialAction.SetNull); // Use SetNull instead of Cascade for safety

      // Step 5: Optional - Log how many notifications couldn't be linked
      migrationBuilder.Sql(@"
                -- This will show in migration output how many notifications remain unlinked
                SELECT 
                    COUNT(*) as UnlinkedNotifications,
                    CONCAT('Notifications linked: ', 
                           (SELECT COUNT(*) FROM Notification WHERE SavedSearchId IS NOT NULL),
                           ', Unlinked: ',
                           COUNT(*)) as Summary
                FROM Notification 
                WHERE SavedSearchId IS NULL;
            ");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.DropForeignKey(
          name: "FK_Notification_SavedSearch_SavedSearchId",
          table: "Notification");

      migrationBuilder.DropIndex(
          name: "IX_Notification_SavedSearchId",
          table: "Notification");

      migrationBuilder.DropColumn(
          name: "SavedSearchId",
          table: "Notification");
    }
  }
}