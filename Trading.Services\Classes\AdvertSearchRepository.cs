using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.Models;
using Trading.Services.Interfaces;

namespace Trading.Services.Classes;

public class AdvertSearchRepository : IAdvertSearchRepository
{
  private readonly TradingContext _context;
  public AdvertSearchRepository(TradingContext context)
  {
    _context = context;
  }

  public async Task<List<SphAdvert>> GetSphAdvertsByPhraseAsync(string phrase, CancellationToken cancellationToken)
  {
    return await _context.SphAdvert
        .Include(x => x.SphLink.Advert.Vehicle)
        .ThenInclude(x => x.LatestValuation).ThenInclude(x => x.VehicleCheckProvider)
        .Include(x => x.SphLink.Advert.Address)
        .Include(x => x.SphLink.Advert.Vehicle.Make)
        .Include(x => x.SphLink.Advert.Vehicle.Model)
        .Include(x => x.SphLink.Advert.Vehicle.Deriv)
        .Include(x => x.SphLink.Advert.Vehicle.Plate)
        .Include(x => x.SphLink.Advert.Vehicle.FuelType)
        .Include(x => x.SphLink.Advert.Vehicle.TransmissionType)
        .Include(x => x.SphLink.Advert.Vehicle.BodyType)
        .Include(x => x.SphLink.Advert.Vehicle.VehicleColour)
        .Include(x => x.SphLink.Advert.Vehicle.VatStatus)
        .Include(x => x.SphLink.Advert.Vehicle.LatestProvenance)
        .Include(x => x.SphLink.Advert.Sale)
        .Where(x => x.phrase == phrase)
        .AsNoTracking()
        .ToListAsync(cancellationToken);
  }

  public async Task<List<SphUnlotted>> GetSphUnlottedByPhraseAsync(string phrase, int? limit, CancellationToken cancellationToken)
  {
    var query = _context.SphUnlotted
        .Include(x => x.SphLink.Advert.Vehicle).ThenInclude(x => x.LatestValuation).ThenInclude(x => x.VehicleCheckProvider)
        .Include(x => x.SphLink.Advert.Sale)
        .AsNoTracking()
        .Where(x => x.phrase == phrase);
    if (limit.HasValue && limit.Value > 0)
      query = query.Take(limit.Value);
    return await query.ToListAsync(cancellationToken);
  }

  public async Task<List<Advert>> GetAdvertsByIdsAsync(List<Guid> ids, CancellationToken cancellationToken)
  {
    return await _context.Adverts
        .AsNoTracking()
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.BodyType)
        .Include(x => x.Vehicle.FuelType)
        .Include(x => x.Vehicle.TransmissionType)
        .Include(x => x.Vehicle.VehicleType)
        .Include(x => x.Vehicle.Plate)
        .Include(x => x.Vehicle.VehicleMedia.Where(z => z.StatusId == (int)Trading.API.Data.Enums.StatusEnum.Active && z.MediaCategoryId == null))
        .Include(x => x.Sale.Address)
        .Where(x => ids.Contains(x.Id))
        .ToListAsync(cancellationToken);
  }

  public async Task<List<Advert>> GetAdvertsByGuidsAsync(List<Guid> guids, CancellationToken cancellationToken)
  {
    return await _context.Adverts
        .AsNoTracking()
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.BodyType)
        .Include(x => x.Vehicle.FuelType)
        .Include(x => x.Vehicle.TransmissionType)
        .Include(x => x.Vehicle.VehicleType)
        .Include(x => x.Vehicle.Plate)
        .Include(x => x.Vehicle.VehicleMedia.Where(z => z.StatusId == (int)Trading.API.Data.Enums.StatusEnum.Active && z.MediaCategoryId == null))
        .Include(x => x.Sale.Address)
        .Where(x => guids.Contains(x.Id))
        .ToListAsync(cancellationToken);
  }

  public async Task<List<SavedSearch>> GetSavedSearchesWithContactsAsync(uint updateFrequency, DateTime? notificationTimeLimit, CancellationToken cancellationToken)
  {
    var query = _context.SavedSearches
        .Include(x => x.Search)
        .Include(x => x.Contact)
        .Where(x => x.UpdateFrequency == updateFrequency && x.StatusId == (uint)Trading.API.Data.Enums.StatusEnum.Active);
    if (notificationTimeLimit.HasValue)
    {
      query = query.Where(x => !x.NotificationTime.HasValue || (x.NotificationTime.HasValue && x.NotificationTime.Value < notificationTimeLimit.Value));
    }
    return await query.ToListAsync(cancellationToken);
  }

  public async Task<List<PriceRange>> GetPriceRangesAsync(CancellationToken cancellationToken)
  {
    return await _context.PriceRanges.ToListAsync(cancellationToken);
  }

  public async Task<List<MileageRange>> GetMileageRangesAsync(CancellationToken cancellationToken)
  {
    return await _context.MileageRanges.ToListAsync(cancellationToken);
  }

  public async Task<List<CapacityRange>> GetCapacityRangesAsync(CancellationToken cancellationToken)
  {
    return await _context.CapacityRanges.ToListAsync(cancellationToken);
  }

  public async Task SaveChangesAsync(CancellationToken cancellationToken)
  {
    await _context.SaveChangesAsync(cancellationToken);
  }

  public async Task<List<Advert>> GetAdvertsByStatusAndNotificationProcessedAsync(Trading.API.Data.Enums.StatusEnum status, DateTime? dailyProcessed, DateTime? immediateProcessed, CancellationToken cancellationToken)
  {
    var query = _context.Adverts.AsNoTracking().Where(x => x.AdvertStatus == API.Data.Enums.AdvertStatusEnum.Active && x.SoldStatus == API.Data.Enums.SoldStatusEnum.Active && x.StatusId == (int)status);
    if (dailyProcessed.HasValue)
      query = query.Where(x => !x.DailyNotificationProcessed.HasValue || x.DailyNotificationProcessed < dailyProcessed);
    if (immediateProcessed.HasValue)
      query = query.Where(x => !x.ImmediateNotificationProcessed.HasValue || x.ImmediateNotificationProcessed < immediateProcessed);
    return await query.ToListAsync(cancellationToken);
  }

  public async Task<List<SavedSearch>> GetSavedSearchesByUpdateFrequencyAndStatusAsync(Trading.API.Data.Enums.UpdateFrequencyEnum updateFrequency, Trading.API.Data.Enums.StatusEnum status, CancellationToken cancellationToken)
  {
    return await _context.SavedSearches
        .Include(x => x.Search)
        .Include(x => x.Contact)
        .Where(x => x.UpdateFrequency == (uint)updateFrequency && x.StatusId == (uint)status)
        .ToListAsync(cancellationToken);
  }

  public void AddSearch(Trading.API.Data.Models.Search search)
  {
    _context.Searches.Add(search);
  }

  public IQueryable<SphAdvert> QuerySphAdvertByPhrase(string phrase)
  {
    return _context.SphAdvert.Where(x => x.phrase == phrase).AsNoTracking();
  }

  public IQueryable<Advert> QueryAdvertByPhrase(string phrase)
  {
    return _context.SphAdvert
        .Where(x => x.phrase == phrase)
        .Select(x => x.SphLink.Advert)
        .AsNoTracking();
  }

  public IQueryable<Advert> QueryAdvertsByIds(List<Guid> ids)
  {
    return _context.Adverts.Where(x => ids.Contains(x.Id)).AsNoTracking();
  }

  public IQueryable<SphAdvert> QuerySphAdvert()
  {
    return _context.SphAdvert.AsNoTracking();
  }

  public IQueryable<Advert> QueryAdverts()
  {
    return _context.Adverts.AsNoTracking();
  }

  public async Task<ContactLocationDTO> GetContactLocationByIdAsync(Guid contactId, CancellationToken cancellationToken)
  {
    return await _context.Contacts
        .AsNoTracking()
        .Select(x => new ContactLocationDTO { Id = x.Id, CountyLat = x.PrimaryAddress.CountyLat, CountyLng = x.PrimaryAddress.CountyLng })
        .FirstOrDefaultAsync(x => x.Id == contactId, cancellationToken);
  }

  public IQueryable<Advert> GetAdvertsByIds(List<Guid> ids)
  {
    return _context.Adverts
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Vehicle.BodyType)
        .Include(x => x.Vehicle.FuelType)
        .Include(x => x.Vehicle.TransmissionType)
        .Include(x => x.Vehicle.VehicleType)
        .Include(x => x.Vehicle.Plate)
        .Include(x => x.Vehicle.VehicleMedia.Where(z => z.StatusId == (int)Trading.API.Data.Enums.StatusEnum.Active && z.MediaCategoryId == null))
        .Include(x => x.Sale.Address)
        .Where(x => ids.Contains(x.Id))
        .AsNoTracking();
  }

  // Repository methods to add:

  public async Task BatchUpdateDailyNotificationProcessed(List<Guid> advertIds, DateTime processedTime, CancellationToken cancellationToken)
  {
    if (!advertIds.Any()) return;

    await _context.Adverts
        .Where(a => advertIds.Contains(a.Id))
        .ExecuteUpdateAsync(
            setters => setters.SetProperty(a => a.DailyNotificationProcessed, processedTime),
            cancellationToken);
  }

  public async Task BatchUpdateImmediateNotificationProcessed(List<Guid> advertIds, DateTime processedTime, CancellationToken cancellationToken)
  {
    if (!advertIds.Any()) return;

    await _context.Adverts
        .Where(a => advertIds.Contains(a.Id))
        .ExecuteUpdateAsync(
            setters => setters.SetProperty(a => a.ImmediateNotificationProcessed, processedTime),
            cancellationToken);
  }

  public async Task BatchUpdateSearchNotificationTimes(Dictionary<Guid, DateTime> searchUpdateTimes, CancellationToken cancellationToken)
  {
    if (!searchUpdateTimes.Any()) return;

    var searchIds = searchUpdateTimes.Keys.ToList();
    var searches = await _context.SavedSearches
        .Where(s => searchIds.Contains(s.Id))
        .ToListAsync(cancellationToken);

    foreach (var search in searches)
    {
      if (searchUpdateTimes.TryGetValue(search.Id, out var notificationTime))
      {
        search.NotificationTime = notificationTime;
      }
    }

    await _context.SaveChangesAsync(cancellationToken);
  }
  public async Task SaveChangesAsync()
  {
    await _context.SaveChangesAsync();
  }
}

public class ContactLocationDTO
{
  public Guid Id { get; set; }
  public double? CountyLat { get; set; }
  public double? CountyLng { get; set; }
}