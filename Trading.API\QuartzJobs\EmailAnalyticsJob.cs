﻿using Quartz;

namespace Trading.API.Remarq.QuartzJobs;

public class EmailAnalyticsJob //: IJob
{
  //private readonly IEmailService _advertSearchService;
  //public InstantAdvertAlertsJob(IAdvertSearchService advertSearchService)
  //{
  //  _advertSearchService = advertSearchService;
  //}

  //public async Task Execute(IJobExecutionContext context)
  //{
  //  await _advertSearchService.ProcessImmediateAlerts(new CancellationToken());
  //}
}
