using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;

namespace Trading.Services.Interfaces;

//public interface IAdvertAlertService
//{
//    Task ProcessDailyAlerts(CancellationToken cancellationToken);
//    Task ProcessImmediateAlerts(CancellationToken cancellationToken);
//    Task<bool> ProcessEmailNotificationMatches(List<Guid> matchingAdverts, string email, Guid alertId, CancellationToken cancellationToken);
//    Task<bool> ProcessEmailNotificationMatches(List<AdvertAlertDTO> allAdverts, List<Guid> matchingAdverts, string email, Guid alertId, CancellationToken cancellationToken);
//    //Task ProcessSearchNotifications(CancellationToken cancellationToken);
//}
