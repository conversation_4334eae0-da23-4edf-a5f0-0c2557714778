using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.Models.InspectCollect;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/container-group")]
  [ApiController]
  [AllowAnonymous]
  public class ICContainerGroupController : ControllerBase
  {
    private readonly ICContainerGroupInterface _icContainerGroupService;

    public ICContainerGroupController(ICContainerGroupInterface serviceInterface)
    {
      _icContainerGroupService = serviceInterface;
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult> Get(Guid id, [FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = new ICContainerGroupSearchDTO();

      if (!String.IsNullOrEmpty(query))
      {
        dto = JsonConvert.DeserializeObject<ICContainerGroupSearchDTO>(query);
      }

      var res = await _icContainerGroupService.Get(id, dto, cancellationToken);
      return Ok(res);
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<ActionResult> Delete(Guid id, CancellationToken cancellationToken)
    {
      var res = await _icContainerGroupService.Delete(id);
      return Ok(res);
    }

    [HttpPost]
    [Route("")]
    public async Task<ActionResult> Post([FromBody] ICContainerGroupCreateDTO dto, CancellationToken cancellationToken)
    {
      var res = await _icContainerGroupService.Create(dto);
      return Ok(res);
    }

    [HttpGet]
    [Route("/api/inspect-collect/container-groups")]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      var dto = JsonConvert.DeserializeObject<ICContainerGroupSearchDTO>(query);
      var res = await _icContainerGroupService.Search(dto, cancellationToken);
      return Ok(res);
    }



    [HttpPatch]
    [Route("{id}")]
    public async Task<IActionResult> Patch(Guid id, JsonPatchDocument<ICContainerGroup> dto)
    {
      var response = await _icContainerGroupService.Patch(id, dto);
      return Ok(response);
    }

    [HttpPost]
    [Route("copy/{id}")]
    public async Task<IActionResult> Copy(Guid id, [FromBody] ICContainerGroupCopyDTO dto)
    {
      try
      {
        var response = await _icContainerGroupService.Copy(id, dto);
        if (response == null)
        {
          return NotFound($"ICContainerGroup with ID {id} not found");
        }
        return Ok(response);
      }
      catch (Exception ex)
      {
        return BadRequest($"Error copying ICContainerGroup: {ex.Message}");
      }
    }

  }
}