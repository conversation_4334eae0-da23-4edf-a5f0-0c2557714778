using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Components.AdvertSearch;
using Trading.API.Data.Models;
using Trading.Services.Classes;

namespace Trading.Services.Interfaces
{
  public interface IAdvertSearchRepository
  {
    Task<List<SphAdvert>> GetSphAdvertsByPhraseAsync(string phrase, CancellationToken cancellationToken);
    Task<List<SphUnlotted>> GetSphUnlottedByPhraseAsync(string phrase, int? limit, CancellationToken cancellationToken);
    Task<List<Advert>> GetAdvertsByIdsAsync(List<Guid> ids, CancellationToken cancellationToken);
    Task<List<Advert>> GetAdvertsByGuidsAsync(List<Guid> guids, CancellationToken cancellationToken);
    Task<List<SavedSearch>> GetSavedSearchesWithContactsAsync(uint updateFrequency, DateTime? notificationTimeLimit, CancellationToken cancellationToken);
    Task<List<PriceRange>> GetPriceRangesAsync(CancellationToken cancellationToken);
    Task<List<MileageRange>> GetMileageRangesAsync(CancellationToken cancellationToken);
    Task<List<CapacityRange>> GetCapacityRangesAsync(CancellationToken cancellationToken);
    Task SaveChangesAsync(CancellationToken cancellationToken);
    IQueryable<SphAdvert> QuerySphAdvertByPhrase(string phrase);
    IQueryable<Advert> QueryAdvertByPhrase(string phrase);
    IQueryable<Advert> QueryAdvertsByIds(List<Guid> ids);
    IQueryable<SphAdvert> QuerySphAdvert();
    IQueryable<Advert> QueryAdverts();
    Task<ContactLocationDTO> GetContactLocationByIdAsync(Guid contactId, CancellationToken cancellationToken);
    Task<List<Advert>> GetAdvertsByStatusAndNotificationProcessedAsync(Trading.API.Data.Enums.StatusEnum status, DateTime? dailyProcessed, DateTime? immediateProcessed, CancellationToken cancellationToken);
    Task<List<SavedSearch>> GetSavedSearchesByUpdateFrequencyAndStatusAsync(Trading.API.Data.Enums.UpdateFrequencyEnum updateFrequency, Trading.API.Data.Enums.StatusEnum status, CancellationToken cancellationToken);
    void AddSearch(Trading.API.Data.Models.Search search);
    IQueryable<Advert> GetAdvertsByIds(List<Guid> ids);
    Task SaveChangesAsync();

    Task BatchUpdateDailyNotificationProcessed(List<Guid> advertIds, DateTime processedTime, CancellationToken cancellationToken);
    Task BatchUpdateImmediateNotificationProcessed(List<Guid> advertIds, DateTime processedTime, CancellationToken cancellationToken);

  }
}