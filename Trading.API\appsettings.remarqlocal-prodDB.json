{
  "ConnectionStrings": {
    "TradingAPIContext": "GuidFormat=TimeSwapBinary16;Server=db.tradesales.com;Database=trading;User=root;Password=******;KeepAlive=10;ConvertZeroDateTime=True;"
  },
  "S3Settings": {
    "AccessKeyID": "********************",
    "SecretAccessKey": "Ed3daWrD+o/P6E3NKYgoy1JgtpWho20ylB+OSPFs",
    "BucketName": "tradesales-media-live",
    "AWSRegion": "eu-west-2",
    "InvoiceURL": "https://tradesales-media-dev.s3.eu-west-2.amazonaws.com/"
  },
  "DefaultPlatform": {
    "PlatformId": 1
  },
  "Cors": {
    "Origins": [
      "https://localhost:44321",
      "https://localhost:44322",
      "https://localhost:44320",
      "https://localhost:5001",
      "https://auth.tradesales.com",
      "https://2750-82-33-75-76.ngrok-free.app"
    ]
  },
  "Portal": {
    "URL": "https://portal.tradesales.com",
    "SupportPhone": "0800 058 4020"
  },
  "CognitoAuth": {
    "Audience": "1627ico7f8365h57ag3953oc5g",
    "Authority": "https://cognito-idp.eu-west-2.amazonaws.com/eu-west-2_XCgDowdx7",
    "RequireHttpsMetadata": false,
    "AWSRegion": "eu-west-2",
    "UserPoolId": "eu-west-2_XCgDowdx7",
    "AccessKey": "********************",
    "SecretAccessKey": "gUKlCt/6B8sh46NSICz9DLMNmYOyEg3HgMNkLwRb",
    "InspectCollect": {
      "Audience": "7bgtmh6m4ill6rtm3mhe5n1suo", // Client ID
      "Authority": "https://cognito-idp.eu-west-2.amazonaws.com/eu-west-2_2okZB5Ulq", // User PoolID
      "RequireHttpsMetadata": "true"
    }
  },
  "UKVehicleData": {
    "APIKey": "936a696b-2a4f-46f9-841b-20ecbf0b800e"
  },
  "SmartFleetSettings": {
    "ApiVersion": "1.0",
    "ApiKey": "4C4505CF-C658-4D69-A2D7-A027DCAE749E"
  },
  "DVLASettings": {
    "ApiKey": "qvOmihs8se7i03DkfQsH837MgsVQf8XY1Z53B9LM",
    "MOTApiKey": "3kZ2Jlf7Z46kyImejl7RC9Nbt62kCebi6mBsszUQ",
    "TenantId": "a455b827-244f-4c97-b5b4-ce5d13b4d00c",
    "ClientId": "e412d098-dfdc-409b-913c-975e0f2a994f",
    "ClientSecret": "****************************************",
    "Scope": "https://tapi.dvsa.gov.uk"
  },
  "Gods": [ "<EMAIL>", "<EMAIL>", "<EMAIL>" ],
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Information",
      "Microsoft": "Information",
      "Microsoft.AspNetCore.SignalR": "Debug",
      "Microsoft.AspNetCore.Http.Connections": "Debug"
    }
  },
  "YoutubeAuth": {
    "clientId": "200577374592-5lf89761sb8jb4ao0leop2jv4r40u5ss.apps.googleusercontent.com",
    "secret": "AhnITU1MC1IPoWPDthgGYGBo",
    "UserId": "HSV3VOPyNdJu5Xwqu1sCGg",
    "CallbackServer": "https://localhost:5001"
  },
  "Stripe": {
    "APIServer": "https://api.stripe.com/",
    "SecretKey": "***********************************************************************************************************",
    "APIKey": "pk_live_51Q6rvoFkvrxV59xa9zKf4biEsH2XgmFkVFVm01WrAxegBnVJoODVpW48sUArGVZrnTdtWn7vYR6vfHv1FrPM8xfw00BCHqxJcn" // live
  },
  "Xero": {
    "ClientId": "B8D0A2CA9D544FCFBB3E7F3037604CBA", // remarq limited production account
    "ClientSecret": "cTeTAM6lUxdAqXCJftZiQxiL55MnsGz82Yf9GCdiIzLEexqo",
    "CallbackUrl": "https://api.tradesales.com/xeroResponse",
    "UserId": "TRADING",
    "AutoRefreshToken": "true",
    //"TenantId": "edaa37d4-79eb-46f6-9414-77f459d4d981", // demo company 
    "TenantId": "d12ea34a-98c6-4b63-beda-17047d1bc455" // REMARQ
  },
  "EmailSettings": {
    "AdminAddress": "<EMAIL>",
    "BccAddress": "<EMAIL>",
    "FromAddress": "<EMAIL>"
  },
  "Cron": {
    "Enabled": "false"
  },
  "SMS": {
    "SID": "",
    "Token": "",
    "FromNumber": ""
  },
  "EventBridgeAPIKey": "@X#(gLcK4g2qn%6",
  "X-Api-Key": "@X#(gLcK4g2qn%6",
  "VehicleVision": {
    "APIKey": "sjxKb2LQLXA51PQkTcZY",
    "CentreId": 72,
    "TemplateId": 56
  },
  "SendInBlue": {
    "APIKey": "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",
    "Sender": "<EMAIL>",
    "SenderName": "TradeSales",
    "MailboxLayerAPIKey": "********************************"
  },
  "AzureStorageSettings": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=bigtest;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ContainerName": "big-data"
  },
  "ServiceConfig": {
    "UseAzureStorage": false, // use S3 if false
    "VehicleLookup": "CapHPI", // will use UKVehicleData by default
    "ShowUnderwriteReserve": true
  },
  "CapHPI": {
    "namespace": "http://webservices.hpi.co.uk/SupplementaryEnquiryV1",
    "url": "https://wss.hpi.co.uk/TradeSoap/services/SupplementaryEnquiryV1/",
    "CustomerCode": "0324187", // live code     // "0920188", (test code)
    "Initials": "BIG",
    "Password": "67DEGREE", // live code        // "TEST0621", (test code)
    "PrimaryProductCode": "HPF12",
    "SupplementaryProductCodes": [ "HPI25", "CAPCD", "ADSMT", "MOT01", "HPIM1" ]
  },
  "ImageKit": {
    "AccountId": "jbpp3b4gqhy",
    "PlatformEndpoint": "live"
  },
  "Kippers": {
    "Default": {
      "External": "https://localhost:44322/assets/images/kippers/4-door-saloon.png",
      "Internal": ""
    },
    "4_Saloon": {
      "External": "https://localhost:44322/assets/images/kippers/4-door-saloon.png",
      "Internal": ""
    }
  },
  "Auth": { // this is used in the AuthController to get user details
    "SecretKey": ",nc.4Q]79/f_c%C{"
  },
  "Archive": {
    "ArchiveAfterDays": 365
  },
  "KlaviyoConfig": {
    "PublicKey": "WqfAZ5",
    "PrivateKey": "*************************************"
  },
  "PlateToVin": {
    "Url": "https://platetovin.com/api",
    "APIKey": "wq9AsqFWZxHOaLt"
  },
  "VinAudit": {
    "Url": "https://api.vinaudit.com/v2/query",
    "ValuesURL": "https://marketvalues.vinaudit.com/getmarketvalue.php",
    "APIKey": "ZGBD3DAPTGZSDF4",
    "UserName": "<EMAIL>",
    "Password": "smlo5644"
  },
  "VoiceHost": {
    "UserName": "<EMAIL>",
    "Password": "CQz7sHhu!drHVme",
    "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
    "CSVEndpoint": "callrecords/csv",
    "BaseAddress": "https://beta.api.voicehost.io/v3/",
    "VoiceHostURL": "https://cp.voicehost.co.uk",
    "AuthEndpoint": "users/auth/login"
  },
  "Facebook": {
    "AppId": "1897723620685779",
    "AppSecret": "89a8abb9e98fa425add90db8f81bff00",
    "UserAccessToken": "EAAa9ZBDZAK99MBO039eh6ZBNKFqiyjzsgfsVA3slew7fg7KsIbI9kNiijzvtryx7R0ZBLpanysug6bcsbEQuq6RrOnRBYZB7fVBX97uUDQpNcepnZAMT8k8kdbyfByMhysRJZCPEbVdFWMW1UxeqKqWvrbqgF6ii6US0NwmQHV5e7F4kZAiDsoS6xIia3CpfZCuujWDyFNWNjklWKsIZCvIdo9LP4v7JHZCXaP2o24ZD",
    "PageId": "122104500722338052", //    281566801717296 ",,
    "PageAccessToken": "EAAa9ZBDZAK99MBOZBR4JDonYZAeXaTp5bMwpX1DdHTwF0ZAZA4E4ecoqBPE7EUnmf5t2JTY77JxaEC2bxw9PJCXU7Ao6CBqma2EJXzz94GwZCe2FRtwThVlCBVCf3dDCvZBw4P6IZB0q7ZB4dwv4CZClZAkqn1kp3cqDVw4j8SCiJwD352m6mp1sjtdn4IdXKaJZBIOKCZAm6g7gh3",
    "InstagramPageId": "17841466988544275"
  },
  "Twitter": {
    "APIKey": "*************************",
    "APIKeySecret": "S3FbuiyqIoFyAfLq1Ct36m26O8sURj5JJgUNJuOntDfMPVPoBF",
    "AccessToken": "1797331347284860928-Ex1iDS8dSlPRfNGfBsWtSKJm7APCx7",
    "AccessTokenSecret": "HN4uGKg5xupxJ3HjR9PULrbUJ7GA2xMtUOe48j2ESUeHB"
  },
  "LinkedIn": {
    "ClientId": "783zrnlov8lg87",
    "ClientSecret": "WPL_AP0.S3IBgP0Ogcz8R92P.Mjg4OTMwMzc5MA==",
    "RedirectURI": "https://localhost:5001/api/linked-in/login-callback",
    "AccessToken": "AQU9Krn7mZuRzUAv-6j9xCt08LQGPNyKGiMFITamxDIDfg3tcEwWMSPmJm3XVDj2rAHhKKc5inBJ04VlngX3VG2-DKvKsKLy1HI5_-QenxCZBsZE6-D8X2uk1Bn_Zgiy00HfNlZ2EIbUHugI5G5tV8ewWf5vzYX6iChseQCxQ_tMg7Ddjd-ifj2hkyHiDbumRFSJ0jbMCViBtMUmtWVmjwmEGbKScJb__lOJC8YrBhTEE_SaF5ySes0ZKkVxqiZ4obtzr8suO9jAvaomTwLKKp0Hefkz48_i9jweL07gx1sXdhZIAZIxRlOjyAfusKzCoEh6ymKfQm56Q3bf95_tkLat9H86fg"
  },
  "Zoom": {
    "ClientId": "8wXpApTCRymNei9USbZUKw",
    "ClientSecret": "bOc0dGoRvVxb6RD13RvNPe2EF8JCajQd",
    "AccountId": "hp0siGP6R8etAhoqENJCYA"
  },
  "Google": {
    "APIKey": "AIzaSyDRREM8vnh8GSDRXLsVt_Mi1tnhJCBYb6I",
    "MapURL": "https://maps.googleapis.com/maps/api"
  },
  "SQSSettings": {
    "AccessKeyID": "********************",
    "SecretAccessKey": "Ed3daWrD+o/P6E3NKYgoy1JgtpWho20ylB+OSPFs",
    "AWSRegion": "eu-west-2",
    "StandardURL": "https://sqs.eu-west-2.amazonaws.com/************/StandardScanQueue.fifo",
    "PriorityURL": "https://sqs.eu-west-2.amazonaws.com/************/PriorityScanQueue.fifo"
  },
  "LambdaFunctions": {
    "EnhancementURL": "https://kj3bqo7f3tpxdml7jowwbeiuz40cmmud.lambda-url.eu-west-2.on.aws/",
    "S3Bucket": "tradesales-media-live",
    "LogoProcessingURL": "https://3ikjwlovxkxyzd4ccrpfvaguym0izaji.lambda-url.eu-west-2.on.aws/",
    "AccessKeyID": "********************",
    "SecretAccessKey": "Ed3daWrD+o/P6E3NKYgoy1JgtpWho20ylB+OSPFs",
    "AWSRegion": "eu-west-2"
  },
  "ZohoCampaign": {
    "ClientID": "1000.6MK3DZO0YPII0CRH595DYQZ4KNUQXX",
    "ClientSecret": "385fad6ce6ce2e15423e1c71178b2e485fc4b4c431",
    "CallbackURL": "https://localhost:5001/api/zoho-campaign/callback",
    "AuthURL": "https://accounts.zoho.eu",
    "EndpointURL": "https://campaigns.zoho.eu",
    "ListKey": "3z2d203feef51e05c2063eb6d8374735d63c46ae8c24c7554052e3c293875128e9"
  },
  "InspectCollect": {
    "RootPath": "inspect-collect/",
    "ContainerSnapShotPath": "container-snapshot/",
    "AssetPath": "assets/",
    "ResponsePath": "response/",
    "URLSuffix": "jbpp3b4gqhy/live"
  },
  "InventApi": {
    "BaseUrl": "https://cag-dev.dotadmin.net",
    "Username": "Q5mIUkk3DkYM",
    "Password": "448255b9667e0f0269a6bc107ba31744",
    "cookieUserName": "pete%40tradesales.com",
    "cookieToken": "a8f939f37d81576b10b7f75cda8d90c1"
  },
  "AutoTrader": {
    "BaseUrl": "https://api.autotrader.co.uk",
    "SandboxBaseUrl": "https://api-sandbox.autotrader.co.uk",
    //"Key": "TradeSalesGroup-Sandbox-16-06-25",
    //"Secret": "********************************",
    // // "66897" sandbox dealerId
    "Key": "Trade-Sales-Group-10-07-25",
    "Secret": "IBZhXFqhst6NToQfyTjvvJVOfcYLB2U0",
    "UseSandbox": false,
    "TimeoutSeconds": 30,
    "TokenRefreshBufferSeconds": 60,
    "RateLimitRetryDelaySeconds": 1,
    "ServiceUnavailableRetryDelaySeconds": 2,
    // 344161 - Charles Hurst Nissan Belfast ,     619856 "-","Donalds" "Volo Ipswitch":
    "DealerId": "619856" //, //"619856"
  },
  "DotAdmin": {
    "BaseUrl": "https://dev-stack-admin.dotadmin.net",
    "Username": "<EMAIL>",
    "Password": "your-password",
    "TimeoutSeconds": 30,
    "TokenRefreshBufferSeconds": 300,
    "DefaultCustomerId": 3787,
    "DefaultLocationId": 12,
    "UseCookieAuth": true
  }
}