﻿using Quartz;
using System.Threading;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.QuartzJobs
{
  public class DailySummaryAlertsJob : IJob
  {
    private readonly IAdvertSearchService _advertSearchService;
    public DailySummaryAlertsJob(IAdvertSearchService advertSearchService)
    {
      _advertSearchService = advertSearchService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
      await _advertSearchService.ProcessDailyAlerts(new CancellationToken());
    }
  }
}
